# Vertoie

AI-powered business application platform that generates custom applications through conversational AI and deploys them across multiple platforms (web, desktop, mobile).

## Overview

Vertoie is a comprehensive platform that combines AI-driven business analysis with cross-platform application generation. Users describe their business needs through natural conversation, and the platform generates tailored applications with full deployment capabilities.

## Architecture

- **Backend**: Rust + Axum (cloud-hosted REST API)
- **Frontend Platform**: SolidJS (main Vertoie website)
- **Customer Apps**: Tauri + SolidJS (cross-platform desktop/mobile/web)
- **Database**: PostgreSQL with multi-schema design
- **LLM**: Groq hosting Qwen3 32B 131k

## Project Structure

```
vertoie/
├── core/                    # Rust + Axum backend API
├── web/                     # SolidJS web platform
├── app/                     # Tauri cross-platform apps
├── shared/                  # Shared components/utilities
├── specs/                   # Project specifications and documentation
└── reference/               # Architecture and design documentation
```

## Prerequisites

Before setting up the development environment, ensure you have:

- **Rust** (stable toolchain)
- **Node.js** (LTS version, 18+)
- **PostgreSQL** (version 14+)
- **Git**
- **direnv** (for environment management)
- **pnpm** (package manager)

## Quick Start

> **Note**: Full setup instructions will be available once the development environment is complete.

1. Clone the repository
2. Install dependencies: `pnpm install`
3. Set up environment variables (see Environment Setup below)
4. Start development servers: `pnpm dev`

## Environment Setup

This project uses `direnv` for environment management:

1. Install direnv: https://direnv.net/docs/installation.html
2. Copy `.envrc.local.example` to `.envrc.local`
3. Edit `.envrc.local` with your local configuration
4. Run `direnv allow` to activate the environment

## Development Scripts

- `pnpm dev` - Start all development servers
- `pnpm build` - Build all components for production
- `pnpm test` - Run all tests
- `pnpm lint` - Run linting for all components
- `pnpm format` - Format code for all components
- `pnpm clean` - Clean all build artifacts

## Key Features

### For Business Users
- Conversational AI interface for describing business needs
- Automatic application generation based on requirements
- Cross-platform deployment (web, desktop, mobile)
- Real-time collaboration and iteration

### For Developers
- Modern Rust backend with Axum framework
- Reactive SolidJS frontend with fine-grained updates
- Cross-platform development with Tauri
- Multi-tenant architecture with schema isolation
- WebSocket support for real-time features

## Development Status

🚧 **In Development** - This project is currently in active development. The development environment setup is in progress.

## Contributing

Please see [CONTRIBUTING.md](CONTRIBUTING.md) for development guidelines and workflow information.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For questions, issues, or contributions, please:
- Open an issue on GitHub
- Check the documentation in the `specs/` directory
- Review architecture decisions in the `reference/` directory
