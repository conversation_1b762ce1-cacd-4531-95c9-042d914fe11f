# Environment Configuration

This document describes the environment variable configuration for the Vertoie development environment.

## Overview

Vertoie uses `direnv` for environment management with a two-file approach:

- **`.envrc`** - Committed base configuration with sensible defaults
- **`.envrc.local`** - Local overrides (gitignored, not committed)

## Setup

1. Install direnv: https://direnv.net/docs/installation.html
2. Copy `.envrc.local.example` to `.envrc.local`
3. Edit `.envrc.local` with your local configuration
4. Run `direnv allow` to activate the environment

## Environment Variables

### Database Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `postgres://postgres:postgres@localhost:5432/vertoie_dev` | Main development database connection |
| `TEST_DATABASE_URL` | `postgres://postgres:postgres@localhost:5432/vertoie_test` | Test database connection |

### Server Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `SERVER_HOST` | `127.0.0.1` | Backend server bind address |
| `SERVER_PORT` | `8000` | Backend server port |
| `RUST_LOG` | `debug` | Rust logging level |
| `RUST_BACKTRACE` | `1` | Enable Rust backtraces |

### Frontend Configuration

| Variable | Default | Description |
|----------|---------|-------------|
| `VITE_API_URL` | `http://localhost:8000` | Frontend API endpoint |
| `VITE_WS_URL` | `ws://localhost:8000` | WebSocket endpoint |

### Development Tools

| Variable | Default | Description |
|----------|---------|-------------|
| `CARGO_WATCH_IGNORE` | `target` | Directories for cargo-watch to ignore |
| `SQLX_OFFLINE` | `true` | Use offline mode for SQLx (CI compatibility) |

### Security

| Variable | Default | Description |
|----------|---------|-------------|
| `JWT_SECRET` | `dev-secret-change-in-production` | JWT signing secret (change for production) |

### External Services

| Variable | Default | Description |
|----------|---------|-------------|
| `GROQ_API_KEY` | _(not set)_ | Groq API key for LLM integration |
| `REDIS_URL` | _(not set)_ | Redis connection URL (optional) |

## Local Customization

Common local customizations in `.envrc.local`:

```bash
# Use different database credentials
export DATABASE_URL="postgres://myuser:mypass@localhost:5432/vertoie_dev"

# Change server port
export SERVER_PORT="3001"
export VITE_API_URL="http://localhost:3001"

# Add API keys
export GROQ_API_KEY="gsk_your_actual_api_key"

# Adjust logging
export RUST_LOG="trace"  # More verbose
# or
export RUST_LOG="warn"   # Less verbose
```

## Helpful Aliases

The `.envrc` file provides several helpful aliases:

- `dev-core` - Start Rust backend with hot-reload
- `dev-web` - Start web frontend development server
- `dev-app` - Start Tauri desktop app in development mode
- `dev-all` - Start all development servers
- `db-migrate` - Run database migrations
- `db-reset` - Reset database and run migrations
- `db-prepare` - Prepare SQLx offline data

## Troubleshooting

### direnv not loading

```bash
# Allow direnv for this directory
direnv allow

# Check if direnv is working
direnv status
```

### Database connection issues

```bash
# Check if PostgreSQL is running
pg_isready

# Create databases if they don't exist
createdb vertoie_dev
createdb vertoie_test
```

### Port conflicts

If the default ports are in use, override them in `.envrc.local`:

```bash
export SERVER_PORT="3001"
export VITE_API_URL="http://localhost:3001"
export VITE_WS_URL="ws://localhost:3001"
```
