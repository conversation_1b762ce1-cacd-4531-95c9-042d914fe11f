# Vertoie Backend Architecture

## Overview

The Vertoie backend is designed as a high-performance, scalable multi-tenant system that powers the AI-driven business management platform. Our architecture prioritizes real-time communication, token-based authentication, and flexible JSONB data modeling to support dynamic AI-generated business modules.

## Core Technology Stack

### Runtime & Framework

- **Language**: Rust 1.70+
- **HTTP Framework**: Axum
  - Built on tokio for async performance
  - Type-safe request handling with extractors
  - Built-in WebSocket support
  - Memory-safe with zero-cost abstractions
  - Excellent ecosystem integration

### Database

- **Primary Database**: PostgreSQL 15+
  - JSONB support for flexible settings and metadata
  - Full-text search capabilities
  - Advanced indexing for performance
  - Multi-tenant schema isolation
  - UUID primary keys for security

### Communication Protocols

#### Real-time Communication

- **WebSockets** for Frontend ↔ Backend communication
  - Low-latency bidirectional communication via Axum WebSocket support
  - Essential for voice command processing
  - Real-time AI module generation feedback
  - Live invoice updates and notifications

#### API Access

- **REST API** for external integrations
  - Third-party developer access
  - Mobile app synchronization
  - Integration with existing business tools
  - Webhook endpoints for external services

### Authentication & Security

#### Token-Based Authentication (Passwordless)

- **Magic Links** for user login
  - No passwords stored or managed
  - Email-based authentication tokens
  - 15-minute expiration for security
  - One-time use tokens

- **Invitation Tokens** for organization membership
  - Organization-scoped invitation system
  - 7-day expiration for invitations
  - Role-based access control

#### Additional Security Measures

- End-to-end encryption for all communications
- API rate limiting and throttling
- RBAC (Role-Based Access Control) for multi-user businesses
- Comprehensive audit logging for compliance
- Soft deletes for data recovery
- IP address and user agent tracking

### Data Storage

#### Primary Database

- **PostgreSQL** 14+
  - ACID compliance for business-critical data
  - Advanced JSON support for flexible AI-generated schemas
  - Full-text search capabilities
  - Robust backup and replication features

#### Schema Versioning Strategy

- **PostgreSQL Schemas** for data model versioning
  - Each AI-generated business module gets its own schema version
  - Enables rollback to previous data models
  - Supports A/B testing of module improvements
  - Schema naming convention: `{business_id}_{module_name}_v{version}`

## Architecture Components

### WebSocket Routing System

#### Implementation Strategy

**Library Choice**: Axum built-in WebSocket support + custom routing layer

- Axum provides native WebSocket support with excellent async integration
- Custom message-based routing system for WebSocket connections
- Pattern matching for route discovery and handler dispatch
- Type-safe message handling with serde serialization

#### Message Router Architecture

```
WebSocket Connection (Axum WebSocket)
├── Connection Upgrade Handler (HTTP → WebSocket)
├── Authentication Handler (JWT/Token verification)
├── Message Parser (JSON with serde deserialization)
├── Route Dispatcher (pattern matching)
│   ├── voice.command.process
│   ├── ai.module.generate
│   ├── invoice.create
│   ├── invoice.update
│   ├── business.sync
│   └── notification.subscribe
└── Response Handler (JSON with correlation ID)
```

#### Message Format Standard

```json
{
  "id": "uuid-correlation-id",
  "type": "voice.command.process",
  "route": "/api/v1/business/123/voice/command",
  "payload": {
    "command": "Bill John for 3 hours of pool cleaning"
  },
  "timestamp": "2025-05-31T12:00:00Z"
}
```

#### Routing Implementation Pattern

**Message-Based Routing** (not URL-based like HTTP):

- Each WebSocket message contains a `type` field for routing
- Pattern: `{domain}.{resource}.{action}` (e.g., `voice.command.process`)
- Route handlers registered with pattern matching
- Middleware chain support for authentication, logging, etc.

**Route Registration Example**:

```rust
let router = WebSocketRouter::new();

// Register route handlers
router.handle("voice.command.process", handle_voice_command);
router.handle("ai.module.generate", handle_module_generation);
router.handle("invoice.*", handle_invoice_operations); // Wildcard support
router.handle("business.*.sync", handle_business_sync); // Pattern matching

// Middleware
router.use_middleware(authentication_middleware);
router.use_middleware(logging_middleware);
router.use_middleware(rate_limiting_middleware);
```

#### Connection Management

- **Connection Registry**: Track active connections by business_id/user_id
- **Connection Pools**: Group connections for efficient broadcast
- **Graceful Shutdown**: Handle connection cleanup on server restart
- **Heartbeat/Ping**: Keep connections alive and detect disconnections

#### Connection Multiplexing Strategy

**Multiple Logical Channels Over Single WebSocket**:

- Each message includes a `channel` field for logical separation
- Clients can operate multiple workflows simultaneously
- Server routes messages to appropriate handlers based on channel + type
- Enables concurrent AI operations without connection overhead

**Channel Types**:

- `voice` - Voice command processing
- `ai-gen-{id}` - AI module generation (unique per generation)
- `invoice-{id}` - Invoice operations (per invoice)
- `business-sync` - Business data synchronization
- `notifications` - Real-time notifications

**Enhanced Message Format**:

```json
{
  "id": "uuid-correlation-id",
  "channel": "ai-gen-abc123",
  "type": "ai.module.progress",
  "payload": {
    "progress": 45,
    "status": "Generating customer forms..."
  },
  "timestamp": "2025-05-31T12:00:00Z"
}
```

#### Routing Considerations

- **Namespace-based routing**: `{domain}.{resource}.{action}` message types
- **Channel-aware routing**: Route by channel + type for logical separation
- **Real-time subscriptions**: Client can subscribe to specific data streams
- **Message acknowledgment**: Ensure reliable delivery of critical updates
- **Connection multiplexing**: Multiple logical channels over single WebSocket

### REST API Architecture

#### API Structure

```
# Example
/api/v1/
├── /auth/
│   ├── POST /login
│   ├── POST /refresh
│   └── POST /logout
├── /business/
│   ├── GET /profile
│   ├── PUT /profile
│   └── GET /modules
├── /modules/
│   ├── GET /{module_id}
│   ├── POST /generate
│   ├── PUT /{module_id}
│   └── DELETE /{module_id}
├── /invoices/
│   ├── GET /
│   ├── POST /
│   ├── GET /{invoice_id}
│   ├── PUT /{invoice_id}
│   └── DELETE /{invoice_id}
└── /voice/
    ├── POST /process
    └── GET /commands
```

### Data Layer Architecture

#### Schema Management System

**Schema Versioning Flow:**

1. AI generates new business module
2. System creates new schema: `business_123_pool_service_v2`
3. Data migration utilities move data from v1 to v2
4. Business operates on v2
5. If issues arise, rollback to v1 is available

**Schema Structure:**

```sql
-- Core business schema (persistent)
CREATE SCHEMA business_123_core;

-- Module-specific versioned schemas
CREATE SCHEMA business_123_pool_service_v1;
CREATE SCHEMA business_123_pool_service_v2;
CREATE SCHEMA business_123_invoicing_v1;

-- Schema metadata tracking
CREATE TABLE schema_versions (
    business_id UUID,
    module_name VARCHAR,
    version INTEGER,
    created_at TIMESTAMP,
    is_active BOOLEAN,
    rollback_available BOOLEAN
);
```

## Implementation Details

### FastHTTP + WebSocket Integration

#### Required Dependencies

```toml
# Cargo.toml
[package]
name = "vertoie-core"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = { version = "0.7", features = ["ws", "macros"] }
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
```

#### Core Server Structure

```rust
// main.rs structure
use axum::{
    extract::ws::{WebSocketUpgrade, WebSocket},
    response::Response,
    routing::{get, post},
    Router,
};
use tower_http::cors::CorsLayer;

#[tokio::main]
async fn main() {
    // Initialize WebSocket router
    let ws_router = create_websocket_router();

    // Create main application router
    let app = Router::new()
        .route("/ws", get(websocket_handler))
        .route("/api/v1/auth/login", post(auth::login))
        .route("/api/v1/business/profile", get(business::get_profile))
        .layer(CorsLayer::permissive())
        .with_state(AppState::new());

    // Start server
    let listener = tokio::net::TcpListener::bind("0.0.0.0:8000").await.unwrap();
    axum::serve(listener, app).await.unwrap();
}

async fn websocket_handler(ws: WebSocketUpgrade) -> Response {
    ws.on_upgrade(handle_websocket)
}
```

#### WebSocket Router Implementation

```rust
// websocket_router.rs
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::RwLock;

pub struct WebSocketRouter {
    routes: HashMap<String, HandlerFunc>,
    middleware: Vec<MiddlewareFunc>,
    conn_mgr: ConnectionManager,
    channels: ChannelManager,
}

#[derive(Serialize, Deserialize)]
pub struct Message {
    pub id: String,
    pub channel: Option<String>,
    pub message_type: String,
    pub route: String,
    pub payload: serde_json::Value,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

type HandlerFunc = Box<dyn Fn(WebSocketContext, Message) -> Result<(), WebSocketError> + Send + Sync>;
type MiddlewareFunc = Box<dyn Fn(HandlerFunc) -> HandlerFunc + Send + Sync>;

func (r *WebSocketRouter) Handle(pattern string, handler HandlerFunc) {
    r.routes[pattern] = handler
}

func (r *WebSocketRouter) Use(middleware MiddlewareFunc) {
    r.middleware = append(r.middleware, middleware)
}

func (r *WebSocketRouter) dispatch(ctx *WebSocketContext, msg *Message) error {
    // Channel-aware routing
    routeKey := r.buildRouteKey(msg.Channel, msg.Type)
    handler, ok := r.findHandler(routeKey)
    if !ok {
        // Fallback to type-only routing
        handler, ok = r.findHandler(msg.Type)
        if !ok {
            return fmt.Errorf("no handler for message type: %s on channel: %s", msg.Type, msg.Channel)
        }
    }

    // Apply middleware chain
    finalHandler := handler
    for i := len(r.middleware) - 1; i >= 0; i-- {
        finalHandler = r.middleware[i](finalHandler)
    }

    return finalHandler(ctx, msg)
}

func (r *WebSocketRouter) buildRouteKey(channel, msgType string) string {
    if channel != "" {
        return fmt.Sprintf("%s:%s", channel, msgType)
    }
    return msgType
}
```

#### Channel Management Implementation

```go
// channel_manager.go
type ChannelManager struct {
    channels map[string]*Channel  // channel_id -> Channel
    mu       sync.RWMutex
}

type Channel struct {
    ID          string
    Type        string           // voice, ai-gen, invoice, etc.
    ConnID      string           // Which connection owns this channel
    Subscribers map[string]bool  // connection_ids subscribed to this channel
    State       interface{}      // Channel-specific state
    CreatedAt   time.Time
    LastActive  time.Time
}

func (cm *ChannelManager) CreateChannel(channelType, connID string) *Channel {
    cm.mu.Lock()
    defer cm.mu.Unlock()

    channel := &Channel{
        ID:          uuid.New().String(),
        Type:        channelType,
        ConnID:      connID,
        Subscribers: make(map[string]bool),
        CreatedAt:   time.Now(),
        LastActive:  time.Now(),
    }

    // Add owner as subscriber
    channel.Subscribers[connID] = true
    cm.channels[channel.ID] = channel

    return channel
}

func (cm *ChannelManager) Subscribe(channelID, connID string) error {
    cm.mu.Lock()
    defer cm.mu.Unlock()

    channel, exists := cm.channels[channelID]
    if !exists {
        return fmt.Errorf("channel %s not found", channelID)
    }

    channel.Subscribers[connID] = true
    channel.LastActive = time.Now()
    return nil
}

func (cm *ChannelManager) Broadcast(channelID string, message *Message) error {
    cm.mu.RLock()
    channel, exists := cm.channels[channelID]
    if !exists {
        cm.mu.RUnlock()
        return fmt.Errorf("channel %s not found", channelID)
    }

    // Copy subscriber list
    subscribers := make([]string, 0, len(channel.Subscribers))
    for connID := range channel.Subscribers {
        subscribers = append(subscribers, connID)
    }
    cm.mu.RUnlock()

    // Send to all subscribers
    for _, connID := range subscribers {
        if ctx, exists := cm.connections[connID]; exists {
            ctx.Send(message)
        }
    }

    return nil
}

func (cm *ChannelManager) CleanupStaleChannels(timeout time.Duration) {
    cm.mu.Lock()
    defer cm.mu.Unlock()

    cutoff := time.Now().Add(-timeout)
    for channelID, channel := range cm.channels {
        if channel.LastActive.Before(cutoff) {
            delete(cm.channels, channelID)
        }
    }
}
```

````

#### Connection Management

```go
// connection_manager.go
type ConnectionManager struct {
    connections map[string]*WebSocketContext  // conn_id -> connection
    mu          sync.RWMutex
    channels    *ChannelManager              // Handle multiplexing
}

type WebSocketContext struct {
    Conn       *websocket.Conn
    BusinessID string
    UserID     string
    ConnID     string
    Router     *WebSocketRouter
    Channels   map[string]*Channel  // Active channels for this connection
    mu         sync.RWMutex
}

func (cm *ConnectionManager) Register(businessID, userID string, conn *websocket.Conn) *WebSocketContext {
    cm.mu.Lock()
    defer cm.mu.Unlock()

    ctx := &WebSocketContext{
        Conn:       conn,
        BusinessID: businessID,
        UserID:     userID,
        ConnID:     uuid.New().String(),
        Channels:   make(map[string]*Channel),
    }

    cm.connections[ctx.ConnID] = ctx
    return ctx
}

func (ctx *WebSocketContext) CreateChannel(channelType string) *Channel {
    ctx.mu.Lock()
    defer ctx.mu.Unlock()

    channel := ctx.Router.connMgr.channels.CreateChannel(channelType, ctx.ConnID)
    ctx.Channels[channel.ID] = channel

    return channel
}

func (ctx *WebSocketContext) Send(message *Message) error {
    return ctx.Conn.WriteJSON(message)
}

func (ctx *WebSocketContext) SendToChannel(channelID string, message *Message) error {
    // Set the channel in the message
    message.Channel = channelID
    return ctx.Router.connMgr.channels.Broadcast(channelID, message)
}

func (ctx *WebSocketContext) Subscribe(channelID string) error {
    ctx.mu.Lock()
    defer ctx.mu.Unlock()

    return ctx.Router.connMgr.channels.Subscribe(channelID, ctx.ConnID)
}

func (cm *ConnectionManager) Unregister(connID string) {
    cm.mu.Lock()
    defer cm.mu.Unlock()

    if ctx, exists := cm.connections[connID]; exists {
        // Cleanup all channels for this connection
        for channelID := range ctx.Channels {
            cm.channels.CleanupConnection(channelID, connID)
        }
        delete(cm.connections, connID)
    }
}
````

````

#### Route Handler Examples

```go
// handlers/voice.go - Simple single-channel operation
func HandleVoiceCommand(ctx *WebSocketContext, msg *Message) error {
    var payload struct {
        Command string `json:"command"`
    }

    if err := json.Unmarshal(msg.Payload, &payload); err != nil {
        return err
    }

    // Process voice command
    result := processVoiceCommand(payload.Command, ctx.BusinessID)

    // Send response on same channel (or default if no channel)
    response := &Message{
        ID:      msg.ID, // Correlation ID
        Channel: msg.Channel,
        Type:    "voice.command.result",
        Payload: map[string]interface{}{
            "success": true,
            "action":  result.Action,
            "data":    result.Data,
        },
        Timestamp: time.Now(),
    }

    return ctx.Send(response)
}

// handlers/ai_module.go - Multi-channel streaming operation
func HandleModuleGeneration(ctx *WebSocketContext, msg *Message) error {
    // Create dedicated channel for this AI generation
    channel := ctx.CreateChannel("ai-generation")

    // Send initial response with channel info
    response := &Message{
        ID:      msg.ID,
        Channel: msg.Channel, // Original channel
        Type:    "ai.module.generation.started",
        Payload: map[string]interface{}{
            "generation_channel": channel.ID,
            "status":            "Starting AI module generation...",
        },
        Timestamp: time.Now(),
    }
    ctx.Send(response)

    // Stream progress on dedicated channel
    go func() {
        for progress := 0; progress <= 100; progress += 10 {
            update := &Message{
                ID:      msg.ID,
                Channel: channel.ID, // Dedicated channel
                Type:    "ai.module.progress",
                Payload: map[string]interface{}{
                    "progress": progress,
                    "status":   fmt.Sprintf("Generating... %d%%", progress),
                },
                Timestamp: time.Now(),
            }
            ctx.SendToChannel(channel.ID, update)
            time.Sleep(100 * time.Millisecond)
        }

        // Final result
        finalResult := &Message{
            ID:      msg.ID,
            Channel: channel.ID,
            Type:    "ai.module.complete",
            Payload: map[string]interface{}{
                "success":     true,
                "module_data": generateModuleData(),
            },
            Timestamp: time.Now(),
        }
        ctx.SendToChannel(channel.ID, finalResult)
    }()

    return nil
}

// handlers/invoice.go - Channel-specific operations
func HandleInvoiceOperations(ctx *WebSocketContext, msg *Message) error {
    // Route based on invoice-specific channel
    switch msg.Type {
    case "invoice.create":
        return handleInvoiceCreate(ctx, msg)
    case "invoice.update":
        return handleInvoiceUpdate(ctx, msg)
    case "invoice.subscribe":
        return handleInvoiceSubscribe(ctx, msg)
    default:
        return fmt.Errorf("unknown invoice operation: %s", msg.Type)
    }
}

func handleInvoiceSubscribe(ctx *WebSocketContext, msg *Message) error {
    var payload struct {
        InvoiceID string `json:"invoice_id"`
    }

    if err := json.Unmarshal(msg.Payload, &payload); err != nil {
        return err
    }

    // Subscribe to invoice-specific channel
    invoiceChannel := fmt.Sprintf("invoice-%s", payload.InvoiceID)
    return ctx.Subscribe(invoiceChannel)
}
````

#### Multiplexing Use Cases

**Concurrent Operations Example**:

```go
// Client can run multiple operations simultaneously:

// 1. Start AI module generation (gets dedicated channel)
{
  "id": "gen-001",
  "channel": "main",
  "type": "ai.module.generate",
  "payload": {"business_type": "pool_service"}
}

// 2. Process voice command while AI is running
{
  "id": "voice-001",
  "channel": "voice",
  "type": "voice.command.process",
  "payload": {"command": "Bill John for 3 hours"}
}

// 3. Subscribe to invoice updates
{
  "id": "sub-001",
  "channel": "notifications",
  "type": "invoice.subscribe",
  "payload": {"invoice_id": "inv-123"}
}

// Server responses come back on appropriate channels:
// AI progress: channel="ai-gen-abc123"
// Voice result: channel="voice"
// Invoice updates: channel="invoice-inv-123"
```

**Benefits of Multiplexing**:

- **Concurrent Operations**: Multiple AI generations, voice commands, invoice operations
- **Isolated Streams**: Each operation gets its own logical channel
- **Efficient Resource Usage**: Single WebSocket connection for all operations
- **Real-time Responsiveness**: No blocking between different operation types
- **Clean State Management**: Channel-specific state isolation

````

### HTTP + WebSocket Coexistence

#### Unified Request Handling

```go
func handleWebSocketUpgrade(ctx *fasthttp.RequestCtx, router *WebSocketRouter) {
    err := websocket.FastHTTPUpgrader.Upgrade(ctx, func(conn *websocket.Conn) {
        defer conn.Close()

        // Extract business/user info from mTLS cert or query params
        businessID := extractBusinessID(ctx)
        userID := extractUserID(ctx)

        // Register connection
        wsCtx := router.connMgr.Register(businessID, userID, conn)

        // Message handling loop
        for {
            var msg Message
            if err := conn.ReadJSON(&msg); err != nil {
                break
            }

            if err := router.dispatch(wsCtx, &msg); err != nil {
                wsCtx.SendError(msg.ID, err.Error())
            }
        }

        // Cleanup on disconnect
        router.connMgr.Unregister(wsCtx.ConnID)
    })

    if err != nil {
        ctx.Error("WebSocket upgrade failed", fasthttp.StatusBadRequest)
    }
}
````

#### Performance Considerations

**Multiplexing Optimizations**:

- **Channel Pooling**: Reuse channel objects to reduce memory allocation
- **Message Routing Cache**: Cache frequently used route patterns
- **Goroutine Management**: Limit concurrent operations per connection to prevent resource exhaustion
- **Channel Cleanup**: Automatic cleanup of inactive channels to prevent memory leaks

**Connection Management Optimizations**:

- **Message Pooling**: Reuse message objects to reduce GC pressure
- **Connection Pooling**: Efficiently manage thousands of concurrent connections
- **Buffer Management**: Use byte pools for WebSocket read/write operations
- **Channel State Compression**: Minimize memory footprint of channel state

**Scaling Considerations**:

- **Channel Sharding**: Distribute channels across multiple server instances
- **Message Routing**: Use Redis Pub/Sub for cross-instance channel communication
- **Connection Affinity**: Route reconnections to same server instance when possible
- **Graceful Degradation**: Fallback strategies when channel limits are reached

```go
// Performance optimizations
var messagePool = sync.Pool{
    New: func() interface{} {
        return &Message{}
    },
}

var channelPool = sync.Pool{
    New: func() interface{} {
        return &Channel{
            Subscribers: make(map[string]bool),
        }
    },
}

func getMessage() *Message {
    return messagePool.Get().(*Message)
}

func putMessage(msg *Message) {
    *msg = Message{} // Reset
    messagePool.Put(msg)
}

func getChannel() *Channel {
    ch := channelPool.Get().(*Channel)
    // Reset channel state
    for k := range ch.Subscribers {
        delete(ch.Subscribers, k)
    }
    return ch
}

// Channel management with limits
const (
    MaxChannelsPerConnection = 50
    MaxConcurrentOperations  = 10
    ChannelCleanupInterval   = 5 * time.Minute
    ChannelTimeout          = 30 * time.Minute
)

func (ctx *WebSocketContext) CreateChannelWithLimits(channelType string) (*Channel, error) {
    ctx.mu.Lock()
    defer ctx.mu.Unlock()

    if len(ctx.Channels) >= MaxChannelsPerConnection {
        return nil, fmt.Errorf("channel limit exceeded: %d", MaxChannelsPerConnection)
    }

    // Count active operations of this type
    activeCount := 0
    for _, ch := range ctx.Channels {
        if ch.Type == channelType && isChannelActive(ch) {
            activeCount++
        }
    }

    if activeCount >= MaxConcurrentOperations {
        return nil, fmt.Errorf("too many concurrent %s operations: %d", channelType, activeCount)
    }

    return ctx.CreateChannel(channelType), nil
}
```

```

### Connection Management

- **Connection Pooling**: Implement connection pools for database and external services
- **WebSocket Scaling**: Use connection multiplexing for handling thousands of concurrent connections
- **Load Balancing**: Horizontal scaling with session affinity for WebSocket connections

### Caching Strategy

- **Redis**: For session data, frequently accessed business configurations
- **In-Memory Caching**: For AI model responses and module templates
- **CDN**: For static assets and generated module resources

### Monitoring & Observability

- **Prometheus**: Metrics collection for performance monitoring
- **OpenTelemetry**: Distributed tracing for request flow analysis
- **Structured Logging**: JSON-formatted logs for better analysis

## AI Integration Points

### Module Generation Pipeline

```

Voice Command → NLP Processing → Business Analysis → Module Generation → Schema Creation → Frontend Update

```

### Real-time AI Features

- Voice command processing with immediate WebSocket feedback
- Streaming AI responses for module generation
- Real-time invoice auto-population as user speaks
- Dynamic form generation based on AI analysis

## Data Migration & Rollback Strategy

### Migration Process

1. **Pre-migration validation**: Check data integrity and dependencies
2. **Schema creation**: Generate new versioned schema
3. **Data transformation**: Apply AI-suggested data model changes
4. **Validation**: Ensure data consistency in new schema
5. **Activation**: Switch active schema pointer
6. **Cleanup**: Archive old schema (with retention policy)

### Rollback Capabilities

- **Instant rollback**: Change active schema pointer
- **Data reconciliation**: Handle any new data created since migration
- **Conflict resolution**: Merge strategies for data conflicts
- **Audit trail**: Complete rollback history and reasoning

## Security Architecture

### mTLS Implementation

- **Certificate Authority**: Internal CA for client certificate generation
- **Certificate Lifecycle**: Automatic renewal and revocation
- **Device Binding**: Certificates tied to specific devices/installations
- **Fallback Authentication**: API key fallback for development/testing

### Data Protection

- **Encryption at Rest**: PostgreSQL TDE (Transparent Data Encryption)
- **Encryption in Transit**: TLS 1.3 for all communications
- **Key Management**: HashiCorp Vault for secret management
- **PII Handling**: Specialized handling for personally identifiable information

## Deployment & DevOps

### Container Strategy

- **Docker**: Containerized backend services
- **Multi-stage builds**: Optimized container images
- **Health checks**: Comprehensive service health monitoring

### Environment Management

- **Development**: Local PostgreSQL + Redis
- **Staging**: Replica of production with sanitized data
- **Production**: High-availability PostgreSQL cluster + Redis cluster

## Future Considerations

### Scalability Roadmap

- **Microservices**: Eventual decomposition into specialized services
- **Event Sourcing**: For complex business process tracking
- **CQRS**: Command Query Responsibility Segregation for read/write optimization

### Technology Evolution

- **AI Model Hosting**: On-premise AI model serving capabilities
- **Edge Computing**: Local processing for voice commands
- **Blockchain Integration**: For immutable audit trails (enterprise feature)

## Open Questions & Decisions Needed

1. **Message Serialization**: JSON vs MessagePack vs Protocol Buffers?

   - **Recommendation**: Start with JSON for development simplicity, migrate to MessagePack for production performance
   - JSON is easier to debug, MessagePack is more efficient for high-frequency messages

2. **WebSocket Route Pattern**: Should we use hierarchical routing like `voice.command.process` or flat routes?

   - **Current approach**: Hierarchical dot notation for better organization and wildcard matching
   - Alternative: RESTful-style routes in message payloads

3. **Connection Scaling**: How to handle connection limits and horizontal scaling?

   - **Options**: Redis Pub/Sub for multi-instance message broadcasting, sticky sessions, or connection sharding
   - **Decision needed**: When to implement horizontal scaling (target: 10K+ concurrent connections)

4. **Database Sharding**: When and how to implement horizontal partitioning?

   - **Trigger**: When single PostgreSQL instance can't handle load
   - **Strategy**: Shard by business_id for data locality

5. **AI Model Integration**: Embedded models vs external API calls?

   - **Phase 1**: External APIs (OpenAI, etc.) for faster development
   - **Phase 2**: Evaluate on-premise models for cost/privacy benefits

6. **Backup Strategy**: Point-in-time recovery requirements and retention policies?

   - **Requirement**: RTO < 1 hour, RPO < 15 minutes for production
   - **Strategy**: PostgreSQL streaming replication + daily snapshots

7. **Message Acknowledgment**: Should we implement reliable message delivery?
   - **Recommendation**: Yes, for critical operations (invoice creation, payment processing)
   - **Implementation**: Message ID tracking + retry logic + dead letter queue

## WebSocket Routing Decision Summary

**Chosen Approach**: Message-based routing with fasthttp/websocket

- **Pros**: High performance, full control over routing logic, easy to scale
- **Cons**: More implementation work than using Gorilla WebSocket + mux
- **Alternative considered**: Gorilla WebSocket with HTTP router integration
- **Decision rationale**: Better performance alignment with fasthttp, more flexible for our real-time AI features

## Next Steps

1. **Proof of Concept**: Implement basic WebSocket routing with fasthttp
2. **mTLS Setup**: Establish certificate generation and validation pipeline
3. **Schema Versioning**: Build migration utilities and rollback mechanisms
4. **API Design**: Finalize REST API specifications and OpenAPI documentation
5. **Performance Testing**: Establish benchmarks for WebSocket and REST performance

---

_This document is a living specification that will evolve as we implement and learn from our architecture decisions._
```
