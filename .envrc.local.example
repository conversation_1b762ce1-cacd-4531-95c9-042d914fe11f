# Vertoie Development Environment Configuration
# This file is committed to the repository and contains base configuration
# Create .envrc.local for local overrides (this file is gitignored)

# Database Configuration
export DATABASE_URL="postgres://postgres:postgres@localhost:5432/vertoie_dev"
export TEST_DATABASE_URL="postgres://postgres:postgres@localhost:5432/vertoie_test"

# Server Configuration
export RUST_LOG="debug"
export RUST_BACKTRACE="1"
export SERVER_HOST="127.0.0.1"
export SERVER_PORT="8000"

# Frontend Configuration
export VITE_API_URL="http://localhost:8000"
export VITE_WS_URL="ws://localhost:8000"

# Development Tools
export CARGO_WATCH_IGNORE="target"
export SQLX_OFFLINE="true"

# JWT Configuration (use a secure secret in production)
export JWT_SECRET="dev-secret-change-in-production"

# LLM Configuration (add your API key in .envrc.local)
# export GROQ_API_KEY="your-groq-api-key-here"

# Redis Configuration (optional for development)
# export REDIS_URL="redis://localhost:6379"

# Load local overrides if they exist
if [ -f .envrc.local ]; then
  source_env .envrc.local
fi

# Helpful development aliases
alias dev-core="cd core && cargo watch -x run"
alias dev-web="cd web && pnpm dev"
alias dev-app="cd app && pnpm tauri dev"
alias dev-all="pnpm dev"

# Database helpers
alias db-migrate="cd core && sqlx migrate run"
alias db-reset="cd core && sqlx database drop -y && sqlx database create && sqlx migrate run"
alias db-prepare="cd core && cargo sqlx prepare"

echo "🚀 Vertoie development environment loaded"
echo "📊 Database: $DATABASE_URL"
echo "🌐 API Server: http://$SERVER_HOST:$SERVER_PORT"
echo "💡 Run 'pnpm dev' to start all development servers"
