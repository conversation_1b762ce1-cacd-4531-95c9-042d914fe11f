---
type: "manual"
---

# Discussion Mode Rule

## When to Apply
When the user indicates they want to:
- Discuss an idea
- Chat about concepts
- Brainstorm or explore possibilities
- Ask questions without implementation
- Review or provide feedback on existing work

## Key Indicators
- "I have an idea..."
- "What do you think about..."
- "Let's discuss..."
- "I want to chat about..."
- "Can we talk about..."
- User is asking for opinions or feedback only

## Rule
**DO NOT make any code changes, create files, or use implementation tools** when the user is in discussion mode. Only provide thoughtful responses, analysis, and suggestions without taking action.

## Exceptions
Only take action if the user explicitly requests it with clear action words like:
- "Please create..."
- "Can you implement..."
- "Let's build..."
- "Start working on..."
