---
type: "agent_requested"
description: "Testing strategies, unit/integration/e2e test patterns, test setup, mocking, CI/CD testing, or test automation for Rust/TypeScript"
---
# Testing Guidelines and Patterns

## Rust Testing Patterns

### Unit Testing
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use sqlx::PgPool;
    use tokio_test;
    
    #[tokio::test]
    async fn test_business_creation() {
        let pool = setup_test_db().await;
        let repo = BusinessRepository::new(pool);
        
        let business_data = CreateBusinessRequest {
            name: "Test Business".to_string(),
            industry: "Technology".to_string(),
            email: "<EMAIL>".to_string(),
        };
        
        let result = repo.create_business(business_data).await;
        
        assert!(result.is_ok());
        let business = result.unwrap();
        assert_eq!(business.name, "Test Business");
        assert_eq!(business.industry, "Technology");
    }
    
    #[tokio::test]
    async fn test_business_not_found() {
        let pool = setup_test_db().await;
        let repo = BusinessRepository::new(pool);
        
        let result = repo.find_by_id(Uuid::new_v4()).await;
        
        assert!(result.is_ok());
        assert!(result.unwrap().is_none());
    }
    
    async fn setup_test_db() -> PgPool {
        let database_url = std::env::var("TEST_DATABASE_URL")
            .expect("TEST_DATABASE_URL must be set");
        
        PgPool::connect(&database_url).await
            .expect("Failed to connect to test database")
    }
}
```

### Integration Testing
```rust
// tests/integration_test.rs
use axum::http::StatusCode;
use axum_test::TestServer;
use serde_json::json;
use vertoie_core::create_app;

#[tokio::test]
async fn test_business_api_endpoints() {
    let app = create_app().await;
    let server = TestServer::new(app).unwrap();
    
    // Test business creation
    let response = server
        .post("/api/v1/businesses")
        .json(&json!({
            "name": "Test Business",
            "industry": "Technology",
            "email": "<EMAIL>"
        }))
        .await;
    
    assert_eq!(response.status_code(), StatusCode::CREATED);
    
    let business: BusinessProfile = response.json();
    assert_eq!(business.name, "Test Business");
    
    // Test business retrieval
    let response = server
        .get(&format!("/api/v1/businesses/{}", business.id))
        .await;
    
    assert_eq!(response.status_code(), StatusCode::OK);
    
    let retrieved: BusinessProfile = response.json();
    assert_eq!(retrieved.id, business.id);
}

#[tokio::test]
async fn test_websocket_connection() {
    let app = create_app().await;
    let server = TestServer::new(app).unwrap();
    
    let mut websocket = server.get_websocket("/ws").await;
    
    // Send test message
    websocket.send_text(json!({
        "id": "test-123",
        "type": "voice.command.process",
        "payload": {
            "command": "Create new customer John Doe"
        }
    }).to_string()).await;
    
    // Receive response
    let response = websocket.recv_text().await.unwrap();
    let message: serde_json::Value = serde_json::from_str(&response).unwrap();
    
    assert_eq!(message["id"], "test-123");
    assert_eq!(message["type"], "voice.command.result");
}
```

### Mock Testing
```rust
use mockall::predicate::*;
use mockall::mock;

// Mock trait for LLM client
mock! {
    LLMClient {}
    
    #[async_trait]
    impl LLMClientTrait for LLMClient {
        async fn generate_business_model(
            &self,
            description: &str,
            context: BusinessContext,
        ) -> Result<BusinessModel, LLMError>;
    }
}

#[tokio::test]
async fn test_business_analysis_with_mock_llm() {
    let mut mock_llm = MockLLMClient::new();
    
    mock_llm
        .expect_generate_business_model()
        .with(eq("Pool cleaning service"), any())
        .times(1)
        .returning(|_, _| {
            Ok(BusinessModel {
                industry: "Pool Service".to_string(),
                modules: vec!["customer_management", "scheduling", "invoicing"],
                schema: serde_json::json!({
                    "customer": {
                        "type": "object",
                        "properties": {
                            "name": {"type": "string"},
                            "address": {"type": "string"},
                            "pool_type": {"type": "string"}
                        }
                    }
                }),
            })
        });
    
    let analyzer = BusinessAnalyzer::new(mock_llm);
    let result = analyzer.analyze("Pool cleaning service").await;
    
    assert!(result.is_ok());
    let model = result.unwrap();
    assert_eq!(model.industry, "Pool Service");
    assert!(model.modules.contains(&"customer_management".to_string()));
}
```

## TypeScript/SolidJS Testing

### Component Testing with Vitest
```typescript
// tests/components/BusinessProfile.test.tsx
import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@solidjs/testing-library';
import { BusinessProfile } from '../src/components/BusinessProfile';

describe('BusinessProfile', () => {
  const mockBusiness = {
    id: '123',
    name: 'Test Business',
    industry: 'Technology',
    email: '<EMAIL>',
  };
  
  it('should render business information', () => {
    render(() => <BusinessProfile business={mockBusiness} />);
    
    expect(screen.getByText('Test Business')).toBeInTheDocument();
    expect(screen.getByText('Technology')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
  
  it('should call onUpdate when form is submitted', async () => {
    const onUpdate = vi.fn();
    
    render(() => (
      <BusinessProfile 
        business={mockBusiness} 
        onUpdate={onUpdate}
        editable={true}
      />
    ));
    
    const nameInput = screen.getByLabelText('Business Name');
    const submitButton = screen.getByText('Save');
    
    fireEvent.input(nameInput, { target: { value: 'Updated Business' } });
    fireEvent.click(submitButton);
    
    expect(onUpdate).toHaveBeenCalledWith({
      ...mockBusiness,
      name: 'Updated Business',
    });
  });
  
  it('should show loading state', () => {
    render(() => <BusinessProfile business={mockBusiness} loading={true} />);
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
```

### Store Testing
```typescript
// tests/stores/businessStore.test.ts
import { describe, it, expect, beforeEach } from 'vitest';
import { businessStore, businessActions, setBusinessStore } from '../src/stores/businessStore';

describe('businessStore', () => {
  beforeEach(() => {
    // Reset store before each test
    setBusinessStore({
      profile: null,
      modules: [],
      loading: false,
      error: null,
    });
  });
  
  it('should initialize with default state', () => {
    expect(businessStore.profile).toBeNull();
    expect(businessStore.modules).toEqual([]);
    expect(businessStore.loading).toBe(false);
    expect(businessStore.error).toBeNull();
  });
  
  it('should update profile', () => {
    const profile = {
      id: '123',
      name: 'Test Business',
      industry: 'Technology',
    };
    
    businessActions.updateProfile(profile);
    
    expect(businessStore.profile).toEqual(profile);
  });
  
  it('should add module', () => {
    const module = {
      id: 'mod-1',
      name: 'Customer Management',
      active: true,
    };
    
    businessActions.addModule(module);
    
    expect(businessStore.modules).toContain(module);
  });
});
```

### API Client Testing
```typescript
// tests/api/ApiClient.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { ApiClient } from '../src/api/ApiClient';

// Mock fetch for web environment
global.fetch = vi.fn();

// Mock Tauri for native environment
vi.mock('@tauri-apps/api/tauri', () => ({
  invoke: vi.fn(),
}));

describe('ApiClient', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset platform detection
    delete (window as any).__TAURI__;
  });
  
  it('should make web request when not in Tauri', async () => {
    const mockResponse = { id: '123', name: 'Test Business' };
    
    (global.fetch as any).mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockResponse),
    });
    
    const result = await ApiClient.getBusinessProfile('123');
    
    expect(fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/v1/businesses/123'),
      expect.objectContaining({
        method: 'GET',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
        }),
      })
    );
    
    expect(result).toEqual(mockResponse);
  });
  
  it('should make Tauri request when in Tauri environment', async () => {
    // Mock Tauri environment
    (window as any).__TAURI__ = true;
    
    const { invoke } = await import('@tauri-apps/api/tauri');
    const mockResponse = { id: '123', name: 'Test Business' };
    
    (invoke as any).mockResolvedValueOnce(mockResponse);
    
    const result = await ApiClient.getBusinessProfile('123');
    
    expect(invoke).toHaveBeenCalledWith('api_request', {
      endpoint: '/api/v1/businesses/123',
      method: 'GET',
      headers: expect.any(Object),
      body: undefined,
    });
    
    expect(result).toEqual(mockResponse);
  });
});
```

## End-to-End Testing

### Playwright Configuration
```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### E2E Test Examples
```typescript
// e2e/business-setup.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Business Setup Flow', () => {
  test('should create new business through conversation', async ({ page }) => {
    await page.goto('/');
    
    // Start business setup
    await page.click('[data-testid="start-setup"]');
    
    // Enter business description
    await page.fill('[data-testid="business-description"]', 
      'I run a pool cleaning service with 50 customers');
    
    await page.click('[data-testid="analyze-business"]');
    
    // Wait for AI analysis
    await expect(page.locator('[data-testid="analysis-result"]')).toBeVisible();
    
    // Verify suggested modules
    await expect(page.locator('text=Customer Management')).toBeVisible();
    await expect(page.locator('text=Scheduling')).toBeVisible();
    await expect(page.locator('text=Invoicing')).toBeVisible();
    
    // Approve and generate
    await page.click('[data-testid="approve-modules"]');
    
    // Wait for application generation
    await expect(page.locator('[data-testid="generation-complete"]')).toBeVisible();
    
    // Verify redirect to dashboard
    await expect(page).toHaveURL(/\/dashboard/);
  });
  
  test('should handle WebSocket connection for real-time updates', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Mock WebSocket messages
    await page.evaluate(() => {
      const ws = new WebSocket('ws://localhost:8000/ws');
      ws.onopen = () => {
        ws.send(JSON.stringify({
          id: 'test-123',
          type: 'ai.generation.progress',
          payload: { progress: 50, status: 'Generating forms...' }
        }));
      };
    });
    
    // Verify real-time update
    await expect(page.locator('[data-testid="progress-bar"]')).toHaveAttribute('value', '50');
    await expect(page.locator('text=Generating forms...')).toBeVisible();
  });
});
```

## Test Database Setup

### Database Migrations for Testing
```sql
-- migrations/test_setup.sql
-- Create test database schema
CREATE SCHEMA IF NOT EXISTS test_vertoie;
CREATE SCHEMA IF NOT EXISTS test_business_123;

-- Insert test data
INSERT INTO test_vertoie.organizations (id, name, industry) 
VALUES ('123e4567-e89b-12d3-a456-************', 'Test Business', 'Technology');

INSERT INTO test_business_123.business_data (schema_name, data_type, data, version_id)
VALUES 
  ('customer', 'customer', '{"name": "John Doe", "email": "<EMAIL>"}', '123e4567-e89b-12d3-a456-************'),
  ('invoice', 'invoice', '{"amount": 100.00, "status": "paid"}', '123e4567-e89b-12d3-a456-************');
```

### Test Utilities
```rust
// tests/utils/mod.rs
use sqlx::{PgPool, Postgres, Transaction};
use uuid::Uuid;

pub struct TestDb {
    pub pool: PgPool,
}

impl TestDb {
    pub async fn new() -> Self {
        let database_url = std::env::var("TEST_DATABASE_URL")
            .expect("TEST_DATABASE_URL must be set");
        
        let pool = PgPool::connect(&database_url).await
            .expect("Failed to connect to test database");
        
        Self { pool }
    }
    
    pub async fn setup_organization(&self, org_id: Uuid) -> Result<(), sqlx::Error> {
        sqlx::query(&format!("CREATE SCHEMA IF NOT EXISTS business_{}", org_id))
            .execute(&self.pool)
            .await?;
        
        // Create tables for organization
        sqlx::query(&format!(
            "CREATE TABLE business_{}.business_data (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                schema_name VARCHAR(100) NOT NULL,
                data_type VARCHAR(100) NOT NULL,
                data JSONB NOT NULL,
                version_id UUID NOT NULL,
                created_at TIMESTAMP DEFAULT NOW()
            )", org_id
        ))
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    pub async fn cleanup(&self) {
        // Clean up test data
        sqlx::query("DROP SCHEMA IF EXISTS test_vertoie CASCADE")
            .execute(&self.pool)
            .await
            .ok();
    }
}
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test-rust:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: vertoie_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      
      - name: Run migrations
        run: |
          cd core
          cargo install sqlx-cli
          sqlx migrate run
        env:
          DATABASE_URL: postgres://postgres:postgres@localhost/vertoie_test
      
      - name: Run tests
        run: cd core && cargo test
        env:
          TEST_DATABASE_URL: postgres://postgres:postgres@localhost/vertoie_test
  
  test-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      
      - name: Install dependencies
        run: |
          cd web && npm install
          cd ../app && npm install
      
      - name: Run tests
        run: |
          cd web && npm test
          cd ../app && npm test
  
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Install Playwright
        run: npx playwright install
      
      - name: Run E2E tests
        run: npx playwright test
```
