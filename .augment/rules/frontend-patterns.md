---
type: "agent_requested"
description: "Example description"
---
# Frontend Patterns and Component Guidelines

## SolidJS Component Patterns

### Component Structure
```typescript
// Standard component pattern
interface ComponentProps {
  // Props should be readonly
  readonly data: BusinessData;
  readonly onUpdate?: (data: BusinessData) => void;
  readonly loading?: boolean;
}

export const BusinessComponent: Component<ComponentProps> = (props) => {
  // Local state with createSignal
  const [localState, setLocalState] = createSignal<string>('');
  
  // Derived state with createMemo
  const computedValue = createMemo(() => {
    return props.data.name.toUpperCase();
  });
  
  // Effects with createEffect
  createEffect(() => {
    console.log('Data changed:', props.data);
  });
  
  return (
    <div class="business-component">
      <Show when={!props.loading} fallback={<LoadingSpinner />}>
        <h2>{computedValue()}</h2>
        <input 
          value={localState()} 
          onInput={(e) => setLocalState(e.currentTarget.value)}
        />
      </Show>
    </div>
  );
};
```

### Store Patterns
```typescript
// Global store for business data
export const [businessStore, setBusinessStore] = createStore({
  profile: null as BusinessProfile | null,
  modules: [] as BusinessModule[],
  loading: false,
  error: null as string | null,
});

// Store actions
export const businessActions = {
  async loadProfile(id: string) {
    setBusinessStore('loading', true);
    setBusinessStore('error', null);
    
    try {
      const profile = await ApiClient.getBusinessProfile(id);
      setBusinessStore({
        profile,
        loading: false,
      });
    } catch (error) {
      setBusinessStore({
        loading: false,
        error: error.message,
      });
    }
  },
  
  updateProfile(updates: Partial<BusinessProfile>) {
    setBusinessStore('profile', (prev) => ({ ...prev, ...updates }));
  },
  
  addModule(module: BusinessModule) {
    setBusinessStore('modules', (prev) => [...prev, module]);
  },
  
  removeModule(moduleId: string) {
    setBusinessStore('modules', (prev) => 
      prev.filter(m => m.id !== moduleId)
    );
  },
};

// Store selectors
export const businessSelectors = {
  getModuleById: (id: string) => 
    createMemo(() => businessStore.modules.find(m => m.id === id)),
  
  getActiveModules: () =>
    createMemo(() => businessStore.modules.filter(m => m.active)),
  
  isLoading: () => businessStore.loading,
  hasError: () => !!businessStore.error,
};
```

### Resource Patterns
```typescript
// API resource with automatic loading states
export const createBusinessResource = (businessId: Accessor<string>) => {
  return createResource(
    businessId,
    async (id: string) => {
      if (!id) return null;
      return ApiClient.getBusinessProfile(id);
    },
    {
      initialValue: null,
    }
  );
};

// Usage in component
const BusinessProfile: Component = () => {
  const params = useParams();
  const businessId = () => params.id;
  
  const [business, { mutate, refetch }] = createBusinessResource(businessId);
  
  const handleUpdate = async (updates: Partial<BusinessProfile>) => {
    // Optimistic update
    mutate((prev) => prev ? { ...prev, ...updates } : null);
    
    try {
      await ApiClient.updateBusinessProfile(businessId(), updates);
      // Refetch to get server state
      refetch();
    } catch (error) {
      // Revert optimistic update
      refetch();
      throw error;
    }
  };
  
  return (
    <div>
      <Show when={business()} fallback={<LoadingSpinner />}>
        {(data) => (
          <BusinessForm 
            business={data()} 
            onUpdate={handleUpdate}
          />
        )}
      </Show>
    </div>
  );
};
```

## Cross-Platform Patterns

### Platform Detection
```typescript
export class PlatformAdapter {
  static isWeb(): boolean {
    return typeof window !== 'undefined' && !window.__TAURI__;
  }
  
  static isTauri(): boolean {
    return typeof window !== 'undefined' && !!window.__TAURI__;
  }
  
  static async showNotification(title: string, body: string): Promise<void> {
    if (this.isTauri()) {
      const { sendNotification } = await import('@tauri-apps/api/notification');
      await sendNotification({ title, body });
    } else {
      // Web notification
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(title, { body });
      }
    }
  }
  
  static async openFile(): Promise<string | null> {
    if (this.isTauri()) {
      const { open } = await import('@tauri-apps/api/dialog');
      const result = await open({
        multiple: false,
        filters: [{ name: 'All Files', extensions: ['*'] }],
      });
      return Array.isArray(result) ? result[0] : result;
    } else {
      // Web file picker
      return new Promise((resolve) => {
        const input = document.createElement('input');
        input.type = 'file';
        input.onchange = (e) => {
          const file = (e.target as HTMLInputElement).files?.[0];
          resolve(file?.name || null);
        };
        input.click();
      });
    }
  }
}
```

### API Client Abstraction
```typescript
export class ApiClient {
  private static baseUrl = import.meta.env.VITE_API_URL;
  
  static async request<T>(
    endpoint: string, 
    options: RequestOptions = {}
  ): Promise<T> {
    if (PlatformAdapter.isTauri()) {
      return this.tauriRequest(endpoint, options);
    } else {
      return this.webRequest(endpoint, options);
    }
  }
  
  private static async webRequest<T>(
    endpoint: string, 
    options: RequestOptions
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getToken()}`,
        ...options.headers,
      },
      body: options.body ? JSON.stringify(options.body) : undefined,
    });
    
    if (!response.ok) {
      throw new ApiError(response.status, await response.text());
    }
    
    return response.json();
  }
  
  private static async tauriRequest<T>(
    endpoint: string, 
    options: RequestOptions
  ): Promise<T> {
    const { invoke } = await import('@tauri-apps/api/tauri');
    
    return invoke('api_request', {
      endpoint,
      method: options.method || 'GET',
      headers: {
        'Authorization': `Bearer ${this.getToken()}`,
        ...options.headers,
      },
      body: options.body,
    });
  }
  
  private static getToken(): string {
    // Get token from storage (localStorage for web, secure storage for Tauri)
    if (PlatformAdapter.isTauri()) {
      // Use Tauri secure storage
      return ''; // TODO: implement
    } else {
      return localStorage.getItem('auth_token') || '';
    }
  }
}
```

## UI Component Library Patterns

### Base Component System
```typescript
// Base button component with variants
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: JSX.Element;
  onClick?: () => void;
}

export const Button: Component<ButtonProps> = (props) => {
  const classes = createMemo(() => {
    const base = 'btn';
    const variant = `btn-${props.variant || 'primary'}`;
    const size = `btn-${props.size || 'md'}`;
    const loading = props.loading ? 'btn-loading' : '';
    const disabled = props.disabled ? 'btn-disabled' : '';
    
    return [base, variant, size, loading, disabled].filter(Boolean).join(' ');
  });
  
  return (
    <button 
      class={classes()}
      disabled={props.disabled || props.loading}
      onClick={props.onClick}
    >
      <Show when={props.loading}>
        <LoadingSpinner size="sm" />
      </Show>
      {props.children}
    </button>
  );
};
```

### Form Patterns
```typescript
// Form validation with signals
export const createFormValidator = <T extends Record<string, any>>(
  initialValues: T,
  validationRules: ValidationRules<T>
) => {
  const [values, setValues] = createStore<T>(initialValues);
  const [errors, setErrors] = createStore<Partial<Record<keyof T, string>>>({});
  const [touched, setTouched] = createStore<Partial<Record<keyof T, boolean>>>({});
  
  const validate = (field?: keyof T) => {
    if (field) {
      const rule = validationRules[field];
      const error = rule ? rule(values[field]) : null;
      setErrors(field, error);
    } else {
      // Validate all fields
      Object.keys(validationRules).forEach((key) => {
        const rule = validationRules[key as keyof T];
        const error = rule ? rule(values[key as keyof T]) : null;
        setErrors(key as keyof T, error);
      });
    }
  };
  
  const setValue = (field: keyof T, value: any) => {
    setValues(field, value);
    setTouched(field, true);
    validate(field);
  };
  
  const isValid = createMemo(() => {
    return Object.values(errors).every(error => !error);
  });
  
  return {
    values,
    errors,
    touched,
    setValue,
    validate,
    isValid,
  };
};

// Usage in form component
const BusinessForm: Component<{ business: BusinessProfile }> = (props) => {
  const form = createFormValidator(
    {
      name: props.business.name,
      industry: props.business.industry,
      email: props.business.email,
    },
    {
      name: (value) => !value ? 'Name is required' : null,
      email: (value) => {
        if (!value) return 'Email is required';
        if (!/\S+@\S+\.\S+/.test(value)) return 'Invalid email';
        return null;
      },
    }
  );
  
  const handleSubmit = (e: Event) => {
    e.preventDefault();
    form.validate();
    
    if (form.isValid()) {
      // Submit form
      console.log('Submitting:', form.values);
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <div class="form-field">
        <label>Business Name</label>
        <input
          type="text"
          value={form.values.name}
          onInput={(e) => form.setValue('name', e.currentTarget.value)}
          class={form.errors.name ? 'error' : ''}
        />
        <Show when={form.touched.name && form.errors.name}>
          <span class="error-message">{form.errors.name}</span>
        </Show>
      </div>
      
      <Button type="submit" disabled={!form.isValid()}>
        Save Business
      </Button>
    </form>
  );
};
```

## WebSocket Integration

### Real-time Updates
```typescript
export const createWebSocketConnection = () => {
  const [connected, setConnected] = createSignal(false);
  const [messages, setMessages] = createSignal<WebSocketMessage[]>([]);
  
  let ws: WebSocket | null = null;
  
  const connect = () => {
    const wsUrl = PlatformAdapter.isTauri() 
      ? 'ws://localhost:8000/ws'
      : `${import.meta.env.VITE_WS_URL}/ws`;
    
    ws = new WebSocket(wsUrl);
    
    ws.onopen = () => {
      setConnected(true);
      console.log('WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      setMessages(prev => [...prev, message]);
    };
    
    ws.onclose = () => {
      setConnected(false);
      console.log('WebSocket disconnected');
      // Reconnect after delay
      setTimeout(connect, 5000);
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  };
  
  const send = (message: WebSocketMessage) => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  };
  
  const disconnect = () => {
    if (ws) {
      ws.close();
      ws = null;
    }
  };
  
  // Auto-connect on creation
  connect();
  
  // Cleanup on component unmount
  onCleanup(disconnect);
  
  return {
    connected,
    messages,
    send,
    disconnect,
  };
};
```

## Performance Optimization

### Lazy Loading
```typescript
// Lazy load components
const LazyBusinessDashboard = lazy(() => import('./BusinessDashboard'));
const LazyModuleEditor = lazy(() => import('./ModuleEditor'));

// Route-based code splitting
const routes = [
  {
    path: '/dashboard',
    component: LazyBusinessDashboard,
  },
  {
    path: '/modules/:id',
    component: LazyModuleEditor,
  },
];
```

### Virtual Scrolling
```typescript
// Virtual list for large datasets
export const VirtualList: Component<{
  items: any[];
  itemHeight: number;
  renderItem: (item: any, index: number) => JSX.Element;
}> = (props) => {
  const [scrollTop, setScrollTop] = createSignal(0);
  const [containerHeight, setContainerHeight] = createSignal(400);
  
  const visibleItems = createMemo(() => {
    const start = Math.floor(scrollTop() / props.itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight() / props.itemHeight) + 1,
      props.items.length
    );
    
    return props.items.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
    }));
  });
  
  return (
    <div 
      class="virtual-list"
      style={{ height: `${containerHeight()}px`, overflow: 'auto' }}
      onScroll={(e) => setScrollTop(e.currentTarget.scrollTop)}
    >
      <div style={{ height: `${props.items.length * props.itemHeight}px` }}>
        <For each={visibleItems()}>
          {({ item, index }) => (
            <div 
              style={{ 
                position: 'absolute',
                top: `${index * props.itemHeight}px`,
                height: `${props.itemHeight}px`,
              }}
            >
              {props.renderItem(item, index)}
            </div>
          )}
        </For>
      </div>
    </div>
  );
};
```
