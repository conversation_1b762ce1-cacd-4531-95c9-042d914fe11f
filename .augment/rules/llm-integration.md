---
type: "agent_requested"
description: "Example description"
---
# LLM Integration Guidelines

## LLM Strategy Overview

### Phase 1: Groq Foundation (0-500 Users)
- **Primary Model**: Qwen3 32B 131k
- **Provider**: Groq hosted
- **Cost**: ~$60-120/month at 500 users
- **Speed**: 2-5 seconds for business model generation
- **Context Window**: 131k tokens for complex business analysis

### Phase 2: Hybrid Architecture (500-2,000 Users)
- Smart caching for popular business models
- Route novel requests to Groq
- 50-70% cost reduction while maintaining speed

### Phase 3: Self-Hosted Transition (2,000+ Users)
- Migrate to self-hosted Qwen3 32B
- 80-90% cost reduction at scale
- Identical model architecture for seamless transition

## Implementation Patterns

### API Abstraction Layer
```rust
pub struct VertoieLLMRouter {
    groq_client: Option<GroqClient>,
    self_hosted_client: Option<SelfHostedClient>,
    use_groq: bool,
}

impl VertoieLLMRouter {
    pub async fn generate_business_model(
        &self,
        description: &str,
        context: BusinessContext,
    ) -> Result<BusinessModel, LLMError> {
        let model = Model::Qwen3_32B; // Single model for all scenarios
        
        if self.use_groq {
            self.groq_generate(description, context, model).await
        } else {
            self.self_hosted_generate(description, context, model).await
        }
    }
    
    async fn groq_generate(
        &self,
        description: &str,
        context: BusinessContext,
        model: Model,
    ) -> Result<BusinessModel, LLMError> {
        let prompt = self.build_prompt(description, context);
        let response = self.groq_client
            .as_ref()
            .ok_or(LLMError::ClientNotConfigured)?
            .generate(model, prompt)
            .await?;
        
        self.parse_business_model(response)
    }
}
```

### Prompt Engineering Patterns

#### Layered Prompting System
```rust
pub struct PromptBuilder {
    universal_patterns: String,
    industry_context: HashMap<String, String>,
    complexity_indicators: Vec<String>,
}

impl PromptBuilder {
    pub fn build_business_analysis_prompt(
        &self,
        description: &str,
        detected_industry: Option<&str>,
        complexity_level: ComplexityLevel,
    ) -> String {
        let mut prompt = String::new();
        
        // 1. Universal business patterns (always included)
        prompt.push_str(&self.universal_patterns);
        
        // 2. Industry-specific context (fuzzy matched)
        if let Some(industry) = detected_industry {
            if let Some(context) = self.industry_context.get(industry) {
                prompt.push_str(context);
            }
        }
        
        // 3. Complexity indicators
        match complexity_level {
            ComplexityLevel::Simple => {
                prompt.push_str("\nFocus on essential business operations and core workflows.");
            }
            ComplexityLevel::Complex => {
                prompt.push_str("\nInclude advanced features, compliance requirements, and multi-location support.");
            }
        }
        
        // 4. User description
        prompt.push_str(&format!("\nBusiness Description: {}", description));
        
        prompt
    }
}
```

#### Business Analysis Pipeline
```rust
pub struct BusinessAnalysisPipeline {
    llm_router: VertoieLLMRouter,
    industry_detector: IndustryDetector,
    requirement_extractor: RequirementExtractor,
    module_selector: ModuleSelector,
    schema_generator: SchemaGenerator,
}

impl BusinessAnalysisPipeline {
    pub async fn analyze_business(
        &self,
        conversation: &str,
    ) -> Result<BusinessAnalysis, AnalysisError> {
        // 1. Industry Detection
        let industry = self.industry_detector.detect(conversation).await?;
        
        // 2. Requirement Extraction
        let requirements = self.requirement_extractor.extract(conversation).await?;
        
        // 3. Module Selection
        let modules = self.module_selector.select_modules(&industry, &requirements).await?;
        
        // 4. Data Model Design
        let schemas = self.schema_generator.generate_schemas(&modules, &requirements).await?;
        
        // 5. Application Generation
        let app_spec = self.generate_application_spec(&modules, &schemas).await?;
        
        Ok(BusinessAnalysis {
            industry,
            requirements,
            modules,
            schemas,
            app_spec,
        })
    }
}
```

### WebSocket Integration for Real-time AI

#### Streaming AI Responses
```rust
pub async fn handle_ai_generation(
    ctx: &WebSocketContext,
    msg: &WebSocketMessage,
) -> Result<(), WebSocketError> {
    // Create dedicated channel for this AI generation
    let channel = ctx.create_channel("ai-generation").await?;
    
    // Send initial response with channel info
    ctx.send_message(WebSocketMessage {
        id: msg.id.clone(),
        channel: Some(msg.channel.clone().unwrap_or_default()),
        message_type: "ai.generation.started".to_string(),
        payload: json!({
            "generation_channel": channel.id,
            "status": "Starting AI module generation..."
        }),
        timestamp: Utc::now(),
    }).await?;
    
    // Stream progress on dedicated channel
    let ctx_clone = ctx.clone();
    let msg_id = msg.id.clone();
    let channel_id = channel.id.clone();
    
    tokio::spawn(async move {
        let mut progress = 0;
        while progress <= 100 {
            let update = WebSocketMessage {
                id: msg_id.clone(),
                channel: Some(channel_id.clone()),
                message_type: "ai.generation.progress".to_string(),
                payload: json!({
                    "progress": progress,
                    "status": format!("Generating... {}%", progress)
                }),
                timestamp: Utc::now(),
            };
            
            let _ = ctx_clone.send_to_channel(&channel_id, update).await;
            progress += 10;
            tokio::time::sleep(Duration::from_millis(100)).await;
        }
        
        // Final result
        let final_result = WebSocketMessage {
            id: msg_id,
            channel: Some(channel_id.clone()),
            message_type: "ai.generation.complete".to_string(),
            payload: json!({
                "success": true,
                "module_data": generate_module_data().await
            }),
            timestamp: Utc::now(),
        };
        
        let _ = ctx_clone.send_to_channel(&channel_id, final_result).await;
    });
    
    Ok(())
}
```

## Business Model Generation

### Single Model Approach
- **Qwen3 32B** handles all business complexity
- Large context window supports complex requirements
- No user confusion about model selection
- Consistent quality across all generations

### Generation Credit System
```rust
pub enum GenerationType {
    Standard,    // 1 credit - Comprehensive analysis
    Custom,      // 2 credits - Interactive Q&A with extended context
}

pub struct GenerationRequest {
    pub business_description: String,
    pub generation_type: GenerationType,
    pub user_id: Uuid,
    pub organization_id: Uuid,
}
```

### Use Case Coverage
All scenarios handled by single Qwen3 32B model:
- Standard service businesses (pool service, lawn care)
- Professional services (consulting, legal, medical)
- Complex operations (multi-state property management)
- Compliance-heavy industries (healthcare, finance)
- Manufacturing with supply chain complexity
- Franchise operations with location variations

## Error Handling and Fallbacks

### LLM Error Types
```rust
#[derive(Debug, thiserror::Error)]
pub enum LLMError {
    #[error("API request failed: {0}")]
    ApiError(String),
    #[error("Rate limit exceeded")]
    RateLimited,
    #[error("Invalid response format")]
    InvalidResponse,
    #[error("Context window exceeded")]
    ContextTooLarge,
    #[error("Model not available")]
    ModelUnavailable,
}
```

### Fallback Strategy
```rust
impl VertoieLLMRouter {
    pub async fn generate_with_fallback(
        &self,
        request: GenerationRequest,
    ) -> Result<BusinessModel, LLMError> {
        // Try primary provider
        match self.generate_business_model(&request.description, request.context).await {
            Ok(result) => Ok(result),
            Err(LLMError::RateLimited) => {
                // Wait and retry
                tokio::time::sleep(Duration::from_secs(1)).await;
                self.generate_business_model(&request.description, request.context).await
            }
            Err(LLMError::ModelUnavailable) => {
                // Switch to backup provider
                self.use_backup_provider(&request).await
            }
            Err(e) => Err(e),
        }
    }
}
```

## Performance Optimization

### Caching Strategy
- Cache popular business model templates
- Cache industry-specific context
- Cache prompt templates
- Implement intelligent cache invalidation

### Monitoring and Analytics
- Track generation success rates
- Monitor response times
- Measure user satisfaction
- Cost tracking per generation
- Usage pattern analysis
