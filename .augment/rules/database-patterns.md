---
type: "agent_requested"
description: "Database design questions, PostgreSQL schemas, JSONB patterns, data migration, repository patterns, or multi-tenant data architecture"
---
# Database Patterns and Schema Design

## Multi-Schema Architecture

### Platform Schema (`vertoie`)
```sql
-- Core platform tables
CREATE SCHEMA vertoie;

-- User management
CREATE TABLE vertoie.users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Organizations (businesses)
CREATE TABLE vertoie.organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    industry VARCHAR(100),
    size VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true
);

-- Subscription plans
CREATE TABLE vertoie.plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price_monthly DECIMAL(10,2),
    price_yearly DECIMAL(10,2),
    features JSONB NOT NULL DEFAULT '[]',
    generation_limit INTEGER,
    is_active BOOLEAN DEFAULT true
);

-- Organization subscriptions
CREATE TABLE vertoie.subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id),
    plan_id UUID NOT NULL REFERENCES vertoie.plans(id),
    status VARCHAR(50) NOT NULL, -- active, cancelled, expired
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Usage tracking
CREATE TABLE vertoie.usage_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES vertoie.organizations(id),
    resource_type VARCHAR(50) NOT NULL, -- generation, api_call, storage
    quantity INTEGER NOT NULL DEFAULT 1,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Organization-Specific Schemas
```sql
-- Dynamic schema creation for each organization
CREATE SCHEMA business_{organization_id};

-- Application versions
CREATE TABLE business_{organization_id}.application_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    version_number INTEGER NOT NULL,
    version_name VARCHAR(255),
    version_type VARCHAR(50) NOT NULL, -- stable, development, historical
    schema_version VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    deployed_at TIMESTAMP,
    status VARCHAR(50) NOT NULL, -- active, testing, archived
    migration_script TEXT,
    rollback_script TEXT,
    git_commit_hash VARCHAR(40),
    UNIQUE(version_number)
);

-- Schema definitions
CREATE TABLE business_{organization_id}.schema_definitions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    version_id UUID NOT NULL REFERENCES business_{organization_id}.application_versions(id),
    schema_name VARCHAR(100) NOT NULL,
    schema_definition JSONB NOT NULL,
    validation_rules JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- Business data (flexible JSONB storage)
CREATE TABLE business_{organization_id}.business_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    schema_name VARCHAR(100) NOT NULL,
    data_type VARCHAR(100) NOT NULL, -- customer, invoice, product, etc.
    data JSONB NOT NULL,
    version_id UUID NOT NULL REFERENCES business_{organization_id}.application_versions(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by UUID,
    is_deleted BOOLEAN DEFAULT false
);

-- Indexes for performance
CREATE INDEX idx_business_data_type ON business_{organization_id}.business_data(data_type);
CREATE INDEX idx_business_data_version ON business_{organization_id}.business_data(version_id);
CREATE INDEX idx_business_data_created ON business_{organization_id}.business_data(created_at);
CREATE INDEX idx_business_data_jsonb ON business_{organization_id}.business_data USING GIN(data);
```

## JSONB Schema Patterns

### Dynamic Schema Validation
```rust
use serde_json::{Value, Map};
use jsonschema::{JSONSchema, ValidationError};

pub struct SchemaValidator {
    schemas: HashMap<String, JSONSchema>,
}

impl SchemaValidator {
    pub fn validate_business_data(
        &self,
        schema_name: &str,
        data: &Value,
    ) -> Result<(), Vec<ValidationError>> {
        let schema = self.schemas.get(schema_name)
            .ok_or_else(|| vec![ValidationError::new("Schema not found")])?;
        
        match schema.validate(data) {
            Ok(_) => Ok(()),
            Err(errors) => Err(errors.collect()),
        }
    }
    
    pub fn add_schema(&mut self, name: String, schema_def: Value) -> Result<(), String> {
        let schema = JSONSchema::compile(&schema_def)
            .map_err(|e| format!("Invalid schema: {}", e))?;
        
        self.schemas.insert(name, schema);
        Ok(())
    }
}
```

### Business Data Models
```rust
#[derive(Serialize, Deserialize, Debug)]
pub struct BusinessDataRecord {
    pub id: Uuid,
    pub schema_name: String,
    pub data_type: String,
    pub data: Value,
    pub version_id: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: Option<Uuid>,
    pub is_deleted: bool,
}

#[derive(Serialize, Deserialize, Debug)]
pub struct SchemaDefinition {
    pub id: Uuid,
    pub version_id: Uuid,
    pub schema_name: String,
    pub schema_definition: Value,
    pub validation_rules: Value,
    pub created_at: DateTime<Utc>,
}
```

## Repository Patterns

### Generic Business Data Repository
```rust
pub struct BusinessDataRepository {
    pool: PgPool,
    organization_id: Uuid,
    schema_validator: SchemaValidator,
}

impl BusinessDataRepository {
    pub async fn create_record(
        &self,
        schema_name: &str,
        data_type: &str,
        data: Value,
        version_id: Uuid,
        created_by: Option<Uuid>,
    ) -> Result<BusinessDataRecord, DatabaseError> {
        // Validate data against schema
        self.schema_validator.validate_business_data(schema_name, &data)?;
        
        let record = sqlx::query_as!(
            BusinessDataRecord,
            r#"
            INSERT INTO business_$1.business_data 
            (schema_name, data_type, data, version_id, created_by)
            VALUES ($2, $3, $4, $5, $6)
            RETURNING *
            "#,
            self.organization_id,
            schema_name,
            data_type,
            data,
            version_id,
            created_by
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(record)
    }
    
    pub async fn find_by_type(
        &self,
        data_type: &str,
        version_id: Option<Uuid>,
    ) -> Result<Vec<BusinessDataRecord>, DatabaseError> {
        let query = if let Some(version) = version_id {
            sqlx::query_as!(
                BusinessDataRecord,
                r#"
                SELECT * FROM business_$1.business_data 
                WHERE data_type = $2 AND version_id = $3 AND is_deleted = false
                ORDER BY created_at DESC
                "#,
                self.organization_id,
                data_type,
                version
            )
        } else {
            sqlx::query_as!(
                BusinessDataRecord,
                r#"
                SELECT * FROM business_$1.business_data 
                WHERE data_type = $2 AND is_deleted = false
                ORDER BY created_at DESC
                "#,
                self.organization_id,
                data_type
            )
        };
        
        let records = query.fetch_all(&self.pool).await?;
        Ok(records)
    }
    
    pub async fn update_record(
        &self,
        id: Uuid,
        data: Value,
        schema_name: &str,
    ) -> Result<BusinessDataRecord, DatabaseError> {
        // Validate updated data
        self.schema_validator.validate_business_data(schema_name, &data)?;
        
        let record = sqlx::query_as!(
            BusinessDataRecord,
            r#"
            UPDATE business_$1.business_data 
            SET data = $2, updated_at = NOW()
            WHERE id = $3 AND is_deleted = false
            RETURNING *
            "#,
            self.organization_id,
            data,
            id
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(record)
    }
    
    pub async fn soft_delete(&self, id: Uuid) -> Result<(), DatabaseError> {
        sqlx::query!(
            r#"
            UPDATE business_$1.business_data 
            SET is_deleted = true, updated_at = NOW()
            WHERE id = $2
            "#,
            self.organization_id,
            id
        )
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
}
```

## Migration Patterns

### Schema Migration System
```rust
pub struct SchemaMigrator {
    pool: PgPool,
    organization_id: Uuid,
}

impl SchemaMigrator {
    pub async fn migrate_to_version(
        &self,
        from_version: Uuid,
        to_version: Uuid,
    ) -> Result<MigrationResult, MigrationError> {
        let migration_script = self.get_migration_script(from_version, to_version).await?;
        
        // Start transaction
        let mut tx = self.pool.begin().await?;
        
        // Create backup
        let backup_id = self.create_backup(&mut tx, from_version).await?;
        
        // Execute migration
        match self.execute_migration(&mut tx, &migration_script).await {
            Ok(_) => {
                tx.commit().await?;
                Ok(MigrationResult {
                    success: true,
                    backup_id: Some(backup_id),
                    migrated_records: 0, // TODO: count
                })
            }
            Err(e) => {
                tx.rollback().await?;
                Err(MigrationError::ExecutionFailed(e.to_string()))
            }
        }
    }
    
    async fn create_backup(
        &self,
        tx: &mut PgTransaction<'_>,
        version_id: Uuid,
    ) -> Result<Uuid, MigrationError> {
        let backup_id = Uuid::new_v4();
        
        sqlx::query!(
            r#"
            CREATE TABLE business_$1.backup_$2 AS 
            SELECT * FROM business_$1.business_data 
            WHERE version_id = $3
            "#,
            self.organization_id,
            backup_id,
            version_id
        )
        .execute(tx)
        .await?;
        
        Ok(backup_id)
    }
}
```

## Performance Optimization

### Indexing Strategy
```sql
-- JSONB indexes for common query patterns
CREATE INDEX idx_customer_email ON business_{org_id}.business_data 
USING GIN((data->>'email')) WHERE data_type = 'customer';

CREATE INDEX idx_invoice_status ON business_{org_id}.business_data 
USING GIN((data->>'status')) WHERE data_type = 'invoice';

CREATE INDEX idx_product_category ON business_{org_id}.business_data 
USING GIN((data->>'category')) WHERE data_type = 'product';

-- Composite indexes for complex queries
CREATE INDEX idx_data_type_created ON business_{org_id}.business_data 
(data_type, created_at DESC);

-- Partial indexes for active records
CREATE INDEX idx_active_records ON business_{org_id}.business_data 
(data_type, created_at) WHERE is_deleted = false;
```

### Query Optimization
```rust
// Use prepared statements for common queries
pub struct PreparedQueries {
    find_by_type: Statement,
    find_by_id: Statement,
    update_data: Statement,
}

impl PreparedQueries {
    pub async fn new(pool: &PgPool, org_id: Uuid) -> Result<Self, sqlx::Error> {
        let find_by_type = pool.prepare(&format!(
            "SELECT * FROM business_{}.business_data WHERE data_type = $1 AND is_deleted = false",
            org_id
        )).await?;
        
        // ... prepare other statements
        
        Ok(Self {
            find_by_type,
            find_by_id,
            update_data,
        })
    }
}
```
