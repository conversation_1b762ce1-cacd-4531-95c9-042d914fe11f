---
type: "agent_requested"
description: "Security implementation, authentication patterns, authorization, data protection, input validation, encryption, or security best practices"
---
# Security Guidelines and Best Practices

## Authentication and Authorization

### JWT Token-Based Authentication
```rust
// JWT token structure
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,        // User ID
    pub org: String,        // Organization ID
    pub exp: usize,         // Expiration time
    pub iat: usize,         // Issued at
    pub roles: Vec<String>, // User roles
}

// Token generation
pub fn generate_jwt(user_id: Uuid, org_id: Uuid, roles: Vec<String>) -> Result<String, JwtError> {
    let expiration = Utc::now()
        .checked_add_signed(chrono::Duration::hours(24))
        .expect("valid timestamp")
        .timestamp();

    let claims = Claims {
        sub: user_id.to_string(),
        org: org_id.to_string(),
        exp: expiration as usize,
        iat: Utc::now().timestamp() as usize,
        roles,
    };

    let secret = std::env::var("JWT_SECRET").expect("JWT_SECRET must be set");
    let token = encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(secret.as_ref()),
    )?;

    Ok(token)
}

// Token validation middleware
pub async fn validate_jwt(
    mut req: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    let auth_header = req
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok())
        .and_then(|header| header.strip_prefix("Bearer "));

    let token = auth_header.ok_or(StatusCode::UNAUTHORIZED)?;
    
    let secret = std::env::var("JWT_SECRET").expect("JWT_SECRET must be set");
    let claims = decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default(),
    )
    .map_err(|_| StatusCode::UNAUTHORIZED)?;

    // Add claims to request extensions
    req.extensions_mut().insert(claims.claims);
    
    Ok(next.run(req).await)
}
```

### Magic Link Authentication
```rust
// Magic link generation
pub struct MagicLinkService {
    redis: RedisPool,
    email_service: EmailService,
}

impl MagicLinkService {
    pub async fn send_magic_link(&self, email: &str) -> Result<(), AuthError> {
        // Generate secure token
        let token = generate_secure_token();
        let expiry = 15 * 60; // 15 minutes
        
        // Store token in Redis with expiration
        let key = format!("magic_link:{}", token);
        self.redis.setex(&key, expiry, email).await?;
        
        // Send email with magic link
        let magic_url = format!("https://vertoie.com/auth/verify?token={}", token);
        self.email_service.send_magic_link(email, &magic_url).await?;
        
        Ok(())
    }
    
    pub async fn verify_magic_link(&self, token: &str) -> Result<String, AuthError> {
        let key = format!("magic_link:{}", token);
        
        // Get email from Redis
        let email: Option<String> = self.redis.get(&key).await?;
        let email = email.ok_or(AuthError::InvalidToken)?;
        
        // Delete token (one-time use)
        self.redis.del(&key).await?;
        
        Ok(email)
    }
}

fn generate_secure_token() -> String {
    use rand::Rng;
    let mut rng = rand::thread_rng();
    (0..32)
        .map(|_| rng.sample(rand::distributions::Alphanumeric) as char)
        .collect()
}
```

### Role-Based Access Control (RBAC)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Role {
    Owner,
    Admin,
    Manager,
    Employee,
    ReadOnly,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Permission {
    ReadBusiness,
    WriteBusiness,
    ReadCustomers,
    WriteCustomers,
    ReadInvoices,
    WriteInvoices,
    ManageUsers,
    ManageSettings,
}

impl Role {
    pub fn permissions(&self) -> Vec<Permission> {
        match self {
            Role::Owner => vec![
                Permission::ReadBusiness,
                Permission::WriteBusiness,
                Permission::ReadCustomers,
                Permission::WriteCustomers,
                Permission::ReadInvoices,
                Permission::WriteInvoices,
                Permission::ManageUsers,
                Permission::ManageSettings,
            ],
            Role::Admin => vec![
                Permission::ReadBusiness,
                Permission::WriteBusiness,
                Permission::ReadCustomers,
                Permission::WriteCustomers,
                Permission::ReadInvoices,
                Permission::WriteInvoices,
                Permission::ManageUsers,
            ],
            Role::Manager => vec![
                Permission::ReadBusiness,
                Permission::ReadCustomers,
                Permission::WriteCustomers,
                Permission::ReadInvoices,
                Permission::WriteInvoices,
            ],
            Role::Employee => vec![
                Permission::ReadCustomers,
                Permission::WriteCustomers,
                Permission::ReadInvoices,
            ],
            Role::ReadOnly => vec![
                Permission::ReadBusiness,
                Permission::ReadCustomers,
                Permission::ReadInvoices,
            ],
        }
    }
}

// Permission checking middleware
pub fn require_permission(permission: Permission) -> impl Fn(Request<Body>, Next<Body>) -> Result<Response, StatusCode> {
    move |req: Request<Body>, next: Next<Body>| {
        let claims = req.extensions().get::<Claims>()
            .ok_or(StatusCode::UNAUTHORIZED)?;
        
        let user_permissions: Vec<Permission> = claims.roles
            .iter()
            .filter_map(|role_str| serde_json::from_str::<Role>(role_str).ok())
            .flat_map(|role| role.permissions())
            .collect();
        
        if !user_permissions.contains(&permission) {
            return Err(StatusCode::FORBIDDEN);
        }
        
        Ok(next.run(req))
    }
}
```

## Data Security

### Multi-Tenant Data Isolation
```rust
// Ensure queries are scoped to organization
pub struct SecureRepository {
    pool: PgPool,
    organization_id: Uuid,
}

impl SecureRepository {
    pub fn new(pool: PgPool, organization_id: Uuid) -> Self {
        Self { pool, organization_id }
    }
    
    pub async fn find_customer(&self, customer_id: Uuid) -> Result<Option<Customer>, DatabaseError> {
        // Always include organization_id in queries
        let customer = sqlx::query_as!(
            Customer,
            r#"
            SELECT * FROM business_$1.business_data 
            WHERE id = $2 AND data_type = 'customer' AND is_deleted = false
            "#,
            self.organization_id,
            customer_id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(customer)
    }
    
    // Prevent cross-tenant data access
    pub async fn update_customer(&self, customer_id: Uuid, data: CustomerData) -> Result<Customer, DatabaseError> {
        // Verify customer belongs to organization before update
        let existing = self.find_customer(customer_id).await?;
        if existing.is_none() {
            return Err(DatabaseError::NotFound);
        }
        
        let updated = sqlx::query_as!(
            Customer,
            r#"
            UPDATE business_$1.business_data 
            SET data = $2, updated_at = NOW()
            WHERE id = $3 AND data_type = 'customer'
            RETURNING *
            "#,
            self.organization_id,
            serde_json::to_value(data)?,
            customer_id
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(updated)
    }
}
```

### Data Encryption
```rust
use aes_gcm::{Aes256Gcm, Key, Nonce};
use aes_gcm::aead::{Aead, NewAead};

pub struct EncryptionService {
    cipher: Aes256Gcm,
}

impl EncryptionService {
    pub fn new() -> Self {
        let key_bytes = std::env::var("ENCRYPTION_KEY")
            .expect("ENCRYPTION_KEY must be set");
        let key = Key::from_slice(key_bytes.as_bytes());
        let cipher = Aes256Gcm::new(key);
        
        Self { cipher }
    }
    
    pub fn encrypt(&self, data: &str) -> Result<String, EncryptionError> {
        let nonce = Nonce::from_slice(b"unique nonce"); // Use proper nonce generation
        let ciphertext = self.cipher.encrypt(nonce, data.as_bytes())
            .map_err(|_| EncryptionError::EncryptionFailed)?;
        
        Ok(base64::encode(ciphertext))
    }
    
    pub fn decrypt(&self, encrypted_data: &str) -> Result<String, EncryptionError> {
        let ciphertext = base64::decode(encrypted_data)
            .map_err(|_| EncryptionError::InvalidFormat)?;
        
        let nonce = Nonce::from_slice(b"unique nonce");
        let plaintext = self.cipher.decrypt(nonce, ciphertext.as_ref())
            .map_err(|_| EncryptionError::DecryptionFailed)?;
        
        String::from_utf8(plaintext)
            .map_err(|_| EncryptionError::InvalidFormat)
    }
}

// Encrypt sensitive fields before storing
#[derive(Serialize, Deserialize)]
pub struct EncryptedCustomer {
    pub id: Uuid,
    pub name: String,
    pub email_encrypted: String, // Encrypted email
    pub phone_encrypted: String, // Encrypted phone
    pub address: String,
    pub created_at: DateTime<Utc>,
}
```

### Input Validation and Sanitization
```rust
use validator::{Validate, ValidationError};
use sanitize_html::{sanitize_str, rules::predefined::DEFAULT};

#[derive(Debug, Validate, Deserialize)]
pub struct CreateCustomerRequest {
    #[validate(length(min = 1, max = 100))]
    pub name: String,
    
    #[validate(email)]
    pub email: String,
    
    #[validate(phone)]
    pub phone: Option<String>,
    
    #[validate(length(max = 500))]
    pub address: Option<String>,
    
    #[validate(length(max = 1000))]
    pub notes: Option<String>,
}

impl CreateCustomerRequest {
    pub fn sanitize(&mut self) {
        // Sanitize HTML content
        if let Some(notes) = &self.notes {
            self.notes = Some(sanitize_str(&DEFAULT, notes).unwrap_or_default());
        }
        
        // Trim whitespace
        self.name = self.name.trim().to_string();
        self.email = self.email.trim().to_lowercase();
        
        if let Some(address) = &self.address {
            self.address = Some(address.trim().to_string());
        }
    }
    
    pub fn validate_and_sanitize(&mut self) -> Result<(), ValidationError> {
        self.sanitize();
        self.validate()?;
        Ok(())
    }
}

// SQL injection prevention with parameterized queries
pub async fn find_customers_by_name(
    pool: &PgPool,
    org_id: Uuid,
    name_pattern: &str,
) -> Result<Vec<Customer>, DatabaseError> {
    // Use parameterized queries - never string concatenation
    let customers = sqlx::query_as!(
        Customer,
        r#"
        SELECT * FROM business_$1.business_data 
        WHERE data_type = 'customer' 
        AND data->>'name' ILIKE $2
        AND is_deleted = false
        ORDER BY data->>'name'
        "#,
        org_id,
        format!("%{}%", name_pattern) // Safe parameter binding
    )
    .fetch_all(pool)
    .await?;
    
    Ok(customers)
}
```

## API Security

### Rate Limiting
```rust
use tower_governor::{GovernorConfigBuilder, GovernorLayer};
use std::time::Duration;

// Rate limiting configuration
pub fn create_rate_limiter() -> GovernorLayer<'static, (), axum::extract::ConnectInfo<std::net::SocketAddr>> {
    let config = GovernorConfigBuilder::default()
        .per_second(10) // 10 requests per second
        .burst_size(20) // Allow bursts up to 20 requests
        .finish()
        .unwrap();
    
    GovernorLayer::new(&config)
}

// Apply rate limiting to routes
let app = Router::new()
    .route("/api/v1/auth/login", post(login))
    .layer(create_rate_limiter())
    .route("/api/v1/businesses", get(get_businesses))
    .layer(validate_jwt);
```

### CORS Configuration
```rust
use tower_http::cors::{CorsLayer, Any};

pub fn create_cors_layer() -> CorsLayer {
    CorsLayer::new()
        .allow_origin([
            "https://vertoie.com".parse().unwrap(),
            "https://app.vertoie.com".parse().unwrap(),
            "http://localhost:3000".parse().unwrap(), // Development only
        ])
        .allow_methods([Method::GET, Method::POST, Method::PUT, Method::DELETE])
        .allow_headers([AUTHORIZATION, CONTENT_TYPE])
        .allow_credentials(true)
        .max_age(Duration::from_secs(3600))
}
```

### Request Logging and Monitoring
```rust
use tracing::{info, warn, error};
use uuid::Uuid;

// Request ID middleware for tracing
pub async fn add_request_id(
    mut req: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    let request_id = Uuid::new_v4();
    req.extensions_mut().insert(request_id);
    
    let method = req.method().clone();
    let uri = req.uri().clone();
    
    info!(
        request_id = %request_id,
        method = %method,
        uri = %uri,
        "Request started"
    );
    
    let start = std::time::Instant::now();
    let response = next.run(req).await;
    let duration = start.elapsed();
    
    info!(
        request_id = %request_id,
        status = response.status().as_u16(),
        duration_ms = duration.as_millis(),
        "Request completed"
    );
    
    Ok(response)
}

// Security event logging
pub fn log_security_event(event_type: &str, user_id: Option<Uuid>, details: &str) {
    warn!(
        event_type = event_type,
        user_id = ?user_id,
        details = details,
        timestamp = %Utc::now(),
        "Security event"
    );
}
```

## WebSocket Security

### WebSocket Authentication
```rust
pub async fn websocket_auth_middleware(
    ws: WebSocketUpgrade,
    Query(params): Query<HashMap<String, String>>,
) -> Result<Response, StatusCode> {
    // Extract token from query parameters or headers
    let token = params.get("token")
        .or_else(|| {
            // Try to get from Authorization header if available
            None // Implement header extraction
        })
        .ok_or(StatusCode::UNAUTHORIZED)?;
    
    // Validate JWT token
    let secret = std::env::var("JWT_SECRET").expect("JWT_SECRET must be set");
    let claims = decode::<Claims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default(),
    )
    .map_err(|_| StatusCode::UNAUTHORIZED)?;
    
    // Upgrade to WebSocket with validated claims
    Ok(ws.on_upgrade(move |socket| handle_websocket(socket, claims.claims)))
}

async fn handle_websocket(socket: WebSocket, claims: Claims) {
    let (sender, mut receiver) = socket.split();
    
    // Create authenticated WebSocket context
    let ctx = AuthenticatedWebSocketContext {
        sender,
        user_id: claims.sub.parse().unwrap(),
        organization_id: claims.org.parse().unwrap(),
        roles: claims.roles,
    };
    
    // Handle messages with authentication context
    while let Some(msg) = receiver.next().await {
        if let Ok(msg) = msg {
            if let Err(e) = handle_authenticated_message(&ctx, msg).await {
                error!("WebSocket message error: {}", e);
                break;
            }
        }
    }
}
```

### Message Validation
```rust
#[derive(Deserialize, Validate)]
pub struct WebSocketMessage {
    #[validate(length(min = 1, max = 100))]
    pub id: String,
    
    #[validate(length(max = 50))]
    pub channel: Option<String>,
    
    #[validate(regex = "MESSAGE_TYPE_REGEX")]
    pub message_type: String,
    
    pub payload: serde_json::Value,
}

lazy_static! {
    static ref MESSAGE_TYPE_REGEX: Regex = Regex::new(r"^[a-z]+\.[a-z]+\.[a-z]+$").unwrap();
}

async fn handle_authenticated_message(
    ctx: &AuthenticatedWebSocketContext,
    raw_msg: Message,
) -> Result<(), WebSocketError> {
    let text = raw_msg.to_text()?;
    
    // Parse and validate message
    let mut message: WebSocketMessage = serde_json::from_str(text)?;
    message.validate()?;
    
    // Check permissions for message type
    if !has_permission_for_message_type(&ctx.roles, &message.message_type) {
        return Err(WebSocketError::Forbidden);
    }
    
    // Route to appropriate handler
    route_message(ctx, message).await
}

fn has_permission_for_message_type(roles: &[String], message_type: &str) -> bool {
    match message_type {
        "voice.command.process" => roles.iter().any(|r| r == "Employee" || r == "Manager" || r == "Admin" || r == "Owner"),
        "ai.module.generate" => roles.iter().any(|r| r == "Admin" || r == "Owner"),
        "business.data.read" => roles.iter().any(|r| r != "ReadOnly" || r == "Employee"),
        _ => false,
    }
}
```

## Frontend Security

### Content Security Policy
```typescript
// CSP configuration for SolidJS apps
export const cspConfig = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'"], // SolidJS requires unsafe-inline
  'style-src': ["'self'", "'unsafe-inline'"],
  'img-src': ["'self'", "data:", "https:"],
  'connect-src': ["'self'", "wss://api.vertoie.com", "https://api.vertoie.com"],
  'font-src': ["'self'"],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  'frame-ancestors': ["'none'"],
};

// Apply CSP headers
export function applyCspHeaders(response: Response): Response {
  const cspValue = Object.entries(cspConfig)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
  
  response.headers.set('Content-Security-Policy', cspValue);
  return response;
}
```

### XSS Prevention
```typescript
// Sanitize user input before rendering
import DOMPurify from 'dompurify';

export const sanitizeHtml = (dirty: string): string => {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: [],
  });
};

// Safe HTML rendering component
export const SafeHtml: Component<{ content: string }> = (props) => {
  const sanitizedContent = createMemo(() => sanitizeHtml(props.content));
  
  return (
    <div innerHTML={sanitizedContent()} />
  );
};

// Input validation
export const validateInput = (input: string, type: 'email' | 'text' | 'number'): boolean => {
  switch (type) {
    case 'email':
      return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(input);
    case 'text':
      return input.length > 0 && input.length <= 1000;
    case 'number':
      return !isNaN(Number(input));
    default:
      return false;
  }
};
```

### Secure Storage
```typescript
// Secure token storage for different platforms
export class SecureStorage {
  static async setToken(token: string): Promise<void> {
    if (PlatformAdapter.isTauri()) {
      // Use Tauri secure storage
      const { Store } = await import('@tauri-apps/plugin-store');
      const store = new Store('.credentials.dat');
      await store.set('auth_token', token);
      await store.save();
    } else {
      // Use secure localStorage with encryption
      const encrypted = await this.encrypt(token);
      localStorage.setItem('auth_token', encrypted);
    }
  }
  
  static async getToken(): Promise<string | null> {
    if (PlatformAdapter.isTauri()) {
      const { Store } = await import('@tauri-apps/plugin-store');
      const store = new Store('.credentials.dat');
      return await store.get('auth_token') || null;
    } else {
      const encrypted = localStorage.getItem('auth_token');
      if (!encrypted) return null;
      return await this.decrypt(encrypted);
    }
  }
  
  private static async encrypt(data: string): Promise<string> {
    // Implement client-side encryption
    return btoa(data); // Simplified - use proper encryption
  }
  
  private static async decrypt(encrypted: string): Promise<string> {
    // Implement client-side decryption
    return atob(encrypted); // Simplified - use proper decryption
  }
}
```
