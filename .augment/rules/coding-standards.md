---
type: "agent_requested"
description: "Example description"
---
# Vertoie Coding Standards

## Rust Standards (Core API)

### Code Organization
- Use `mod.rs` files for module organization
- Separate concerns: routes, models, services, utilities
- Follow Rust naming conventions (snake_case for functions/variables)
- Use `pub(crate)` for internal APIs

### Error Handling
```rust
// Use custom error types with thiserror
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    #[error("Authentication failed")]
    Unauthorized,
    #[error("Business not found: {id}")]
    BusinessNotFound { id: Uuid },
}

// Result type alias
pub type ApiResult<T> = Result<T, ApiError>;
```

### Database Patterns
```rust
// Use SQLx with compile-time query checking
#[derive(sqlx::FromRow, Serialize, Deserialize)]
pub struct BusinessProfile {
    pub id: Uuid,
    pub name: String,
    pub industry: String,
    pub created_at: DateTime<Utc>,
}

// Repository pattern for database operations
pub struct BusinessRepository {
    pool: PgPool,
}

impl BusinessRepository {
    pub async fn find_by_id(&self, id: Uuid) -> ApiResult<Option<BusinessProfile>> {
        let business = sqlx::query_as!(
            BusinessProfile,
            "SELECT id, name, industry, created_at FROM businesses WHERE id = $1",
            id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(business)
    }
}
```

### WebSocket Patterns
```rust
// Message-based routing with serde
#[derive(Serialize, Deserialize)]
pub struct WebSocketMessage {
    pub id: String,
    pub channel: Option<String>,
    pub message_type: String,
    pub payload: serde_json::Value,
    pub timestamp: DateTime<Utc>,
}

// Handler function signature
pub type HandlerFunc = Box<dyn Fn(WebSocketContext, WebSocketMessage) -> Result<(), WebSocketError> + Send + Sync>;
```

## TypeScript/SolidJS Standards

### Component Organization
```typescript
// Use PascalCase for components
export const BusinessProfile: Component<BusinessProfileProps> = (props) => {
  return (
    <div class="business-profile">
      {/* Component content */}
    </div>
  );
};

// Props interface
interface BusinessProfileProps {
  businessId: string;
  onUpdate?: (profile: BusinessProfile) => void;
}
```

### State Management
```typescript
// Use SolidJS stores for global state
export const [businessStore, setBusinessStore] = createStore({
  profile: null as BusinessProfile | null,
  modules: [] as BusinessModule[],
  loading: false,
});

// Actions object for store operations
export const businessActions = {
  async loadProfile(id: string) {
    setBusinessStore('loading', true);
    try {
      const profile = await api.getBusinessProfile(id);
      setBusinessStore({ profile, loading: false });
    } catch (error) {
      setBusinessStore('loading', false);
      throw error;
    }
  },
};
```

### API Client Patterns
```typescript
// Platform-agnostic API client
export class ApiClient {
  private static baseUrl = import.meta.env.VITE_API_URL;
  
  static async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    if (PlatformAdapter.isWeb()) {
      return this.webRequest(endpoint, options);
    } else {
      return this.tauriRequest(endpoint, options);
    }
  }
  
  private static async webRequest<T>(endpoint: string, options: RequestOptions): Promise<T> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      body: options.body ? JSON.stringify(options.body) : undefined,
    });
    
    if (!response.ok) {
      throw new ApiError(response.status, await response.text());
    }
    
    return response.json();
  }
  
  private static async tauriRequest<T>(endpoint: string, options: RequestOptions): Promise<T> {
    return invoke('api_request', {
      endpoint,
      method: options.method || 'GET',
      headers: options.headers || {},
      body: options.body,
    });
  }
}
```

## General Standards

### File Naming
- Rust: `snake_case.rs`
- TypeScript: `kebab-case.ts` or `PascalCase.tsx` for components
- Directories: `kebab-case`

### Documentation
- Use rustdoc for Rust code
- Use JSDoc for TypeScript
- Include examples in documentation
- Document public APIs thoroughly

### Testing
```rust
// Rust unit tests
#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_business_creation() {
        // Test implementation
    }
}
```

```typescript
// TypeScript tests with Vitest
import { describe, it, expect } from 'vitest';

describe('BusinessProfile', () => {
  it('should render business information', () => {
    // Test implementation
  });
});
```

### Environment Configuration
- Use `.env` files for configuration
- Validate environment variables at startup
- Use different configs for dev/staging/production
- Never commit secrets to version control

### Logging
```rust
// Structured logging with tracing
use tracing::{info, warn, error, instrument};

#[instrument(skip(pool))]
pub async fn create_business(pool: &PgPool, business: CreateBusinessRequest) -> ApiResult<Business> {
    info!("Creating new business: {}", business.name);
    // Implementation
}
```

### Error Handling Best Practices
- Use Result types consistently
- Provide meaningful error messages
- Log errors with context
- Handle errors gracefully in UI
- Use error boundaries in SolidJS components
