---
type: "agent_requested"
description: "Questions about system architecture, folder structure, multi-schema database design, WebSocket patterns, cross-platform deployment, or performance optimization"
---
# Vertoie Architecture Patterns

## Folder Structure
```
vertoie/
├── core/                           # Rust + Axum REST API server
│   ├── src/
│   │   ├── main.rs                # Server entry point
│   │   ├── routes/                # API route handlers
│   │   ├── models/                # Database models
│   │   ├── services/              # Business logic
│   │   ├── websocket/             # WebSocket handling
│   │   └── llm/                   # LLM integration
│   ├── migrations/                # Database migrations
│   └── Cargo.toml                 # Rust dependencies
├── app/                           # Customer applications (Tauri + SolidJS)
│   ├── components/                # Shared UI components
│   ├── src/                       # SolidJS frontend
│   ├── src-tauri/                 # Rust backend for Tauri
│   ├── web/                       # Web deployment layer
│   └── package.json               # Node.js dependencies
└── web/                           # Main Vertoie site (SolidJS)
    ├── src/
    ├── components/
    └── package.json
```

## Database Architecture

### Multi-Schema Design
- `vertoie` schema: Platform data (users, organizations, plans)
- `{organization_id}` schema: User business data with versioning
- JSONB for flexible business data models
- Schema versioning for application evolution

### Version Management
- Stable, development, and historical versions
- Git-based versioning for generated applications
- Automated data migration between schema versions
- Rollback capabilities with audit trails

## WebSocket Architecture

### Message-Based Routing
- Pattern: `{domain}.{resource}.{action}` (e.g., `voice.command.process`)
- Channel multiplexing over single WebSocket connection
- Real-time AI generation feedback
- Connection management with heartbeat/ping

### Channel Types
- `voice`: Voice command processing
- `ai-gen-{id}`: AI module generation
- `invoice-{id}`: Invoice operations
- `business-sync`: Data synchronization
- `notifications`: Real-time notifications

## Cross-Platform Strategy

### Platform Detection
```typescript
export class PlatformAdapter {
  static isWeb(): boolean {
    return !window.__TAURI__;
  }
  
  static async apiCall(endpoint: string, data: any) {
    if (this.isWeb()) {
      // Direct HTTP calls to core API
      return fetch(`${CORE_API_URL}${endpoint}`, options);
    } else {
      // Use Tauri commands
      return invoke('api_call', { endpoint, data });
    }
  }
}
```

### Deployment Options
1. **Tauri Native**: Desktop/mobile with native integration
2. **Web Application**: Browser-based universal access
3. **Progressive Web App**: Offline capabilities
4. **Hybrid**: User choice of platform

## Security Patterns

### Authentication
- JWT tokens with magic links (passwordless)
- Invitation tokens for organization membership
- mTLS for enhanced security
- API rate limiting and throttling

### Data Protection
- Multi-tenant data isolation
- Encryption at rest and in transit
- RBAC for multi-user businesses
- Comprehensive audit logging
- Soft deletes for data recovery

## Performance Patterns

### Rust Core Benefits
- Memory safety without garbage collection
- Excellent async/await performance
- Compile-time error prevention
- Near C-level performance

### SolidJS Benefits
- Fine-grained reactivity
- Small bundle size (~10MB vs ~100MB Electron)
- No virtual DOM overhead
- Excellent TypeScript integration

### Caching Strategy
- Redis for session data and configurations
- In-memory caching for AI responses
- CDN for static assets
- Connection pooling for databases
