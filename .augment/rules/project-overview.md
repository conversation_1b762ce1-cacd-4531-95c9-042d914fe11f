---
type: "agent_requested"
description: "Example description"
---
# Vertoie Project Overview

## Project Description
Vertoie is an AI-generated business software builder that creates custom business management applications through natural language conversations. The platform generates tailored modules and data models based on each business's specific needs using LLM technology.

## Core Architecture
- **Backend**: Rust + Axum (cloud-hosted REST API)
- **Frontend Platform**: SolidJS (main Vertoie website)
- **Customer Apps**: Tauri + SolidJS (cross-platform desktop/mobile/web)
- **Database**: PostgreSQL with multi-schema design
- **LLM**: Groq hosting Qwen3 32B 131k (transitioning to self-hosted)

## Key Components

### Core API (`core/`)
- Rust + Axum REST API server
- WebSocket support for real-time communication
- Multi-tenant data isolation
- LLM integration for AI-generated modules
- JWT authentication with magic links

### Web Platform (`web/`)
- SolidJS web application
- User onboarding and business setup
- AI conversation interface
- Application management and deployment
- Billing and subscription management

### Customer Applications (`app/`)
- Tauri + SolidJS for native desktop/mobile apps
- Web deployment for browser access
- Business-specific workflows and modules
- Real-time synchronization with core API
- Offline support (Tauri only)

## Business Model
- AI generates custom business modules through conversation
- Multi-tenant SaaS with tiered pricing
- Cross-platform deployment (desktop, mobile, web)
- Version management with schema evolution
- Industry-agnostic approach supporting any service business

## Development Phases
1. **Foundation**: Basic backend, database, component library
2. **AI Generation**: Business analysis, module generation, testing
3. **Platform Enhancement**: Advanced conversation, customization tools
4. **Production Readiness**: User management, billing, monitoring

## Key Technologies
- **Rust**: Memory-safe, high-performance backend
- **Axum**: Modern async web framework
- **SolidJS**: Fine-grained reactive UI framework
- **Tauri**: Cross-platform app framework
- **PostgreSQL**: JSONB support for flexible schemas
- **WebSockets**: Real-time communication
- **LLM Integration**: Groq/Qwen3 for business analysis
