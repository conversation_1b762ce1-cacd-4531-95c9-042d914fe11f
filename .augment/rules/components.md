---
type: "agent_requested"
description: "SolidJS component architecture rules for Vertoie UI library"
---
# Vertoie UI Component Architecture Rules

## Component Design Principles

### Clean Component Architecture
- Each component must have a single, clear responsibility
- Use composition over complex prop drilling
- Follow functional component patterns with SolidJS primitives
- Keep components under 200 lines when possible
- Extract complex logic into custom hooks/utilities

### Theme System Integration
- **ALWAYS** use theme properties, never hardcoded values
- Use semantic density properties: `density.horizontalPadding`, `density.verticalPadding`, `density.height`
- **NEVER** use scale-based properties: `density.sm`, `density.md`, `density.lg`
- Use semantic colors: `colors.onSurface`, `colors.surface`, `colors.primary`
- Support both light and dark modes automatically

```tsx
// ✅ Good: Proper theme integration
import { Component } from 'solid-js';
import { useTheme } from '@vertoie/ui';

interface VButtonProps {
  children: any;
  onClick?: () => void;
  variant?: 'primary' | 'secondary';
}

const VButton: Component<VButtonProps> = (props) => {
  const theme = useTheme();

  return (
    <button
      class="v-button"
      style={{
        padding: `${theme.density.verticalPadding}px ${theme.density.horizontalPadding}px`,
        'background-color': theme.colors.primary,
        'border-radius': `${theme.cornerRadius.button}px`,
        color: theme.colors.onPrimary,
      }}
      onClick={props.onClick}
    >
      {props.children}
    </button>
  );
};

// ❌ Bad: Hardcoded values
const BadButton: Component = (props) => {
  return (
    <button
      style={{
        padding: '16px', // Hardcoded
        'background-color': 'blue', // Hardcoded
        'border-radius': '8px', // Hardcoded
      }}
    >
      {props.children}
    </button>
  );
};
```

## Component Naming Conventions

### Component Names
- All components must start with `V` prefix: `VButton`, `VInput`, `VCalendar`
- Use descriptive names: `VDatePicker` not `VPicker`
- Group related components: `VCheckbox`, `VCheckboxGroup`
- Variants use descriptive suffixes: `VButtonVariant.primary`, `VInputType.email`

### File Organization
```
src/components/
├── buttons/
│   ├── VButton.tsx
│   └── VButtonVariant.ts
├── inputs/
│   ├── VInput.tsx
│   ├── VInputType.ts
│   └── VTextArea.tsx
├── calendar/
│   ├── VCalendar.tsx
│   ├── VCalendarEvent.ts
│   └── VDatePicker.tsx
```

## API Design Standards

### Component Props Patterns
```tsx
// ✅ Good: Clean, consistent API
interface VButtonProps {
  onPressed?: () => void;
  children: any;
  variant?: VButtonVariant;
  disabled?: boolean;
  class?: string;
}

const VButton: Component<VButtonProps> = (props) => {
  const merged = mergeProps(
    { variant: VButtonVariant.primary, disabled: false },
    props
  );

  return (
    <button
      class={`v-button ${merged.class || ''}`}
      onClick={merged.onPressed}
      disabled={merged.disabled}
    >
      {merged.children}
    </button>
  );
};
```

### Property Guidelines
- Use optional properties with sensible defaults
- Use proper TypeScript types for all props
- Use descriptive prop names
- Group related properties into interfaces when needed

### Callback Conventions
```tsx
// ✅ Good: Descriptive callback names
interface CalendarProps {
  onDateSelected?: (date: Date) => void;
  onEventTapped?: (event: VCalendarEvent) => void;
  onTextChanged?: (text: string) => void;
}

// ❌ Bad: Generic callback names
interface BadProps {
  callback?: Function;
  onTap?: () => void; // Too generic for specific actions
}
```

## Dynamic Data Handling

### LLM-Generated Content Support
- Design components to handle dynamic JSONB data structures
- Use `Map<String, dynamic>` for flexible data models
- Implement runtime validation for dynamic schemas
- Provide fallbacks for missing or invalid data

```tsx
// ✅ Good: Flexible data handling
interface FieldDefinition {
  type?: string;
  required?: boolean;
  label?: string;
  options?: string[];
}

interface VDynamicFormFieldProps {
  fieldDefinition: FieldDefinition;
  onChanged: (value: any) => void;
}

const VDynamicFormField: Component<VDynamicFormFieldProps> = (props) => {
  const fieldType = () => props.fieldDefinition.type || 'text';
  const isRequired = () => props.fieldDefinition.required || false;
  const label = () => props.fieldDefinition.label || 'Field';

  return (
    <div class="dynamic-form-field">
      <label class={isRequired() ? 'required' : ''}>
        {label()}
      </label>
      {buildFieldByType(fieldType(), props.onChanged)}
    </div>
  );
};
```

### Calendar System Integration
- Follow the composable calendar architecture
- Use VCalendarEvent model for all calendar data
- Support color coding (status, category, client-based)
- Implement availability management features
- Use proper event rendering with overflow handling

## Accessibility Requirements

### Screen Reader Support
- All interactive components must have proper semantics
- Use `Semantics` widget for custom components
- Provide meaningful labels and hints
- Support voice-over navigation

```tsx
// ✅ Good: Accessibility support
return (
  <div
    role="button"
    tabIndex={0}
    aria-label={`Calendar event: ${event.title}`}
    aria-describedby="event-hint"
    onClick={onEventTapped}
    onKeyDown={(e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        onEventTapped?.();
      }
    }}
  >
    {eventWidget}
    <span id="event-hint" class="sr-only">
      Press Enter or Space to edit event
    </span>
  </div>
);
```

### Keyboard Navigation
- Support tab navigation for all interactive elements
- Implement proper focus management
- Use `FocusScope` for complex components
- Handle keyboard shortcuts where appropriate

### High Contrast Support
- Use theme colors that automatically support high contrast
- Test components in both light and dark modes
- Ensure proper color contrast ratios (WCAG AA compliance)
- Use semantic colors that adapt to system settings

## State Management Patterns

### Local State
- Use `useState` for simple component state
- Keep state as local as possible
- Use proper disposal for controllers and listeners

```dart
// ✅ Good: Proper state management
class VInput extends StatefulWidget {
  @override
  State<VInput> createState() => _VInputState();
}

class _VInputState extends State<VInput> {
  late final TextEditingController _controller;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }
}
```

### Theme State
- Always use `VTheme.of(context)` to access theme
- Never cache theme values across builds
- Support theme changes at runtime
- Use theme-aware color calculations

## Performance Optimization

### Rendering Performance
- Use `const` constructors whenever possible
- Implement proper `build` method optimization
- Use `RepaintBoundary` for expensive components
- Avoid unnecessary rebuilds with proper key usage

### Memory Management
- Dispose of all controllers and listeners
- Use weak references for long-lived callbacks
- Implement proper lifecycle management
- Avoid memory leaks in stateful components

```dart
// ✅ Good: Performance optimization
class VCalendar extends StatefulWidget {
  const VCalendar({
    super.key,
    required this.events,
    this.onEventTapped,
  });

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: CustomPaint(
        painter: CalendarPainter(
          events: widget.events,
          theme: VTheme.of(context),
        ),
        child: gestureLayer,
      ),
    );
  }
}
```

## Testing Requirements

### Widget Tests
- Test all public API methods and properties
- Test theme integration across density settings
- Test accessibility features
- Mock external dependencies

### Golden Tests
- Create golden tests for visual regression testing
- Test all component variants and states
- Test both light and dark themes
- Test different density settings

### Accessibility Tests
- Use semantic testing helpers
- Test screen reader announcements
- Verify keyboard navigation
- Test high contrast support

```dart
// ✅ Good: Comprehensive testing
testWidgets('VButton supports all variants', (tester) async {
  for (final variant in VButtonVariant.values) {
    await tester.pumpWidget(
      VTestApp(
        child: VButton(
          onPressed: () {},
          variant: variant,
          child: Text('Test'),
        ),
      ),
    );
    
    expect(find.byType(VButton), findsOneWidget);
    await expectLater(
      find.byType(VButton),
      matchesGoldenFile('button_${variant.name}.png'),
    );
  }
});
```

## Documentation Standards

### Component Documentation
- Document all public properties and methods
- Provide usage examples for complex components
- Include accessibility considerations
- Document theme integration points

### Code Comments
- Comment complex business logic only
- Explain "why" not "what"
- Document any workarounds or platform-specific code
- Keep comments up-to-date with code changes

## Industry Module Integration

### Business Logic Separation
- Components should be UI-focused, not business logic
- Use callbacks to communicate with business logic
- Support industry-specific customization through themes
- Design for reusability across different business types

### Calendar-Centric Design
- Many components should integrate with calendar system
- Support appointment scheduling workflows
- Handle client/resource management
- Enable availability and booking management

### Voice Command Integration Points
- Design components with voice command accessibility
- Use clear, semantic labels for voice recognition
- Support voice-driven navigation and actions
- Provide audio feedback for important actions