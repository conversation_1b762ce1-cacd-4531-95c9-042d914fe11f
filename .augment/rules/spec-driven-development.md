---
type: "always_apply"
---

# Spec-Driven Development Workflow

## Overview
Follow a structured 3-stage approach for all development work to ensure clear requirements, thoughtful design, and organized execution.

## Workflow Stages

### 1. Requirements Stage
- Create `specs/<task-name>/requirements.md`
- Include:
  - **Introduction**: Brief overview and context
  - **Requirements**: Functional and non-functional requirements
  - **Acceptance Criteria**: Clear, testable criteria for completion
- Must be reviewed and approved before proceeding

### 2. Design Stage  
- Create `specs/<task-name>/design.md`
- Include:
  - **Architecture**: High-level design decisions
  - **Implementation Plan**: Technical approach and key components
  - **Dependencies**: External libraries, services, or prerequisites
  - **Risk Assessment**: Potential challenges and mitigation strategies
- Must be reviewed and approved before proceeding

### 3. Tasks Stage
- Create `specs/<task-name>/tasks.md`
- Break work into:
  - **Main Tasks**: Logical units of work (~20 minutes each)
  - **Sub-tasks**: Granular steps within main tasks
  - **Dependencies**: Task ordering and prerequisites
- Use task management tools to track progress
- Must be reviewed and approved before implementation

## File Structure
```
specs/
├── <task-name>/
│   ├── requirements.md
│   ├── design.md
│   └── tasks.md
```

## Process Flow
1. Create requirements.md → Review → Approve
2. Create design.md → Review → Approve  
3. Create tasks.md → Review → Approve
4. Execute tasks using task management tools
5. Always update and mark tasks in tasks.md as done when they are finished

## Benefits
- Reduces rework through upfront planning
- Ensures alignment on scope and approach
- Creates documentation trail for decisions
- Enables better estimation and tracking
