---
type: "agent_requested"
description: "Example description"
---
# Deployment and DevOps Guidelines

## Development Environment Setup

### Prerequisites
```bash
# Rust development
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update stable
cargo install cargo-watch sqlx-cli

# Node.js for SolidJS
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install 18
nvm use 18
npm install -g pnpm

# Tauri CLI
cargo install tauri-cli
npm install -g @tauri-apps/cli

# Database
brew install postgresql  # macOS
# or
sudo apt-get install postgresql postgresql-contrib  # Ubuntu
```

### Local Development Setup
```bash
# Clone repository
git clone https://github.com/vertoie/vertoie.git
cd vertoie

# Setup environment variables
cp .env.example .env
# Edit .env with local configuration

# Setup database
createdb vertoie_dev
cd core
sqlx migrate run

# Start development servers
# Terminal 1: Backend
cd core && cargo watch -x run

# Terminal 2: Web frontend
cd web && pnpm dev

# Terminal 3: Tauri app
cd app && pnpm tauri dev
```

### Environment Configuration
```bash
# .env.example
DATABASE_URL=postgres://username:password@localhost/vertoie_dev
TEST_DATABASE_URL=postgres://username:password@localhost/vertoie_test
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret-here
GROQ_API_KEY=your-groq-api-key
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
```

## Build Configuration

### Rust Backend Build
```toml
# core/Cargo.toml
[package]
name = "vertoie-core"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = { version = "0.7", features = ["ws", "macros"] }
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
uuid = { version = "1.0", features = ["v4", "serde"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }
tracing = "0.1"
tracing-subscriber = "0.3"

[profile.release]
lto = true
codegen-units = 1
panic = "abort"
```

### Frontend Build Configuration
```json
// web/package.json
{
  "name": "vertoie-web",
  "version": "0.1.0",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint src --ext .ts,.tsx",
    "type-check": "tsc --noEmit"
  },
  "dependencies": {
    "solid-js": "^1.8.0",
    "@solidjs/router": "^0.10.0",
    "solid-start": "^0.4.0"
  },
  "devDependencies": {
    "vite": "^5.0.0",
    "vite-plugin-solid": "^2.8.0",
    "typescript": "^5.0.0",
    "vitest": "^1.0.0",
    "@solidjs/testing-library": "^0.8.0"
  }
}
```

```typescript
// web/vite.config.ts
import { defineConfig } from 'vite';
import solid from 'vite-plugin-solid';

export default defineConfig({
  plugins: [solid()],
  build: {
    target: 'esnext',
    minify: 'esbuild',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['solid-js', '@solidjs/router'],
        },
      },
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': 'http://localhost:8000',
      '/ws': {
        target: 'ws://localhost:8000',
        ws: true,
      },
    },
  },
});
```

### Tauri Configuration
```json
// app/src-tauri/tauri.conf.json
{
  "build": {
    "beforeDevCommand": "pnpm dev",
    "beforeBuildCommand": "pnpm build",
    "devPath": "http://localhost:1420",
    "distDir": "../dist"
  },
  "package": {
    "productName": "Vertoie Business App",
    "version": "0.1.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "shell": {
        "all": false,
        "open": true
      },
      "dialog": {
        "all": false,
        "open": true,
        "save": true
      },
      "notification": {
        "all": true
      },
      "http": {
        "all": true,
        "request": true
      }
    },
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.vertoie.business-app",
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/icon.icns",
        "icons/icon.ico"
      ]
    },
    "security": {
      "csp": null
    },
    "windows": [
      {
        "fullscreen": false,
        "resizable": true,
        "title": "Vertoie Business App",
        "width": 1200,
        "height": 800,
        "minWidth": 800,
        "minHeight": 600
      }
    ]
  }
}
```

## Docker Configuration

### Backend Dockerfile
```dockerfile
# core/Dockerfile
FROM rust:1.75 as builder

WORKDIR /app
COPY Cargo.toml Cargo.lock ./
COPY src ./src
COPY migrations ./migrations

# Build dependencies first for better caching
RUN cargo build --release --bin vertoie-core

FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY --from=builder /app/target/release/vertoie-core ./
COPY --from=builder /app/migrations ./migrations

EXPOSE 8000

CMD ["./vertoie-core"]
```

### Web Frontend Dockerfile
```dockerfile
# web/Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install

COPY . .
RUN pnpm build

FROM nginx:alpine

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: vertoie
      POSTGRES_USER: vertoie
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  core:
    build: ./core
    environment:
      DATABASE_URL: postgres://vertoie:${POSTGRES_PASSWORD}@postgres:5432/vertoie
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      GROQ_API_KEY: ${GROQ_API_KEY}
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis

  web:
    build: ./web
    ports:
      - "80:80"
    depends_on:
      - core

volumes:
  postgres_data:
  redis_data:
```

## CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy Vertoie

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: vertoie_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          
      - name: Install pnpm
        run: npm install -g pnpm
        
      - name: Cache Rust dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.cargo/registry
            ~/.cargo/git
            core/target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          
      - name: Cache Node dependencies
        uses: actions/cache@v3
        with:
          path: |
            web/node_modules
            app/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/pnpm-lock.yaml') }}

      - name: Run Rust tests
        run: |
          cd core
          cargo test
        env:
          TEST_DATABASE_URL: postgres://postgres:postgres@localhost/vertoie_test

      - name: Run Frontend tests
        run: |
          cd web && pnpm install && pnpm test
          cd ../app && pnpm install && pnpm test

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Core image
        uses: docker/build-push-action@v5
        with:
          context: ./core
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/core:latest

      - name: Build and push Web image
        uses: docker/build-push-action@v5
        with:
          context: ./web
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/web:latest

  build-tauri:
    needs: test
    strategy:
      matrix:
        platform: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.platform }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18
          
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          
      - name: Install dependencies (Ubuntu)
        if: matrix.platform == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libgtk-3-dev libwebkit2gtk-4.0-dev libappindicator3-dev librsvg2-dev patchelf

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Build Tauri app
        run: |
          cd app
          pnpm install
          pnpm tauri build

      - name: Upload artifacts
        uses: actions/upload-artifact@v3
        with:
          name: tauri-${{ matrix.platform }}
          path: |
            app/src-tauri/target/release/bundle/
            !app/src-tauri/target/release/bundle/**/.*

  deploy:
    needs: [build-and-push]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Deploy to production
        run: |
          # Add deployment script here
          echo "Deploying to production..."
```

## Production Deployment

### Infrastructure as Code (Terraform)
```hcl
# infrastructure/main.tf
terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  token = var.do_token
}

# VPC
resource "digitalocean_vpc" "vertoie" {
  name   = "vertoie-vpc"
  region = var.region
}

# Database
resource "digitalocean_database_cluster" "postgres" {
  name       = "vertoie-postgres"
  engine     = "pg"
  version    = "15"
  size       = "db-s-1vcpu-1gb"
  region     = var.region
  node_count = 1
}

# Redis
resource "digitalocean_database_cluster" "redis" {
  name       = "vertoie-redis"
  engine     = "redis"
  version    = "7"
  size       = "db-s-1vcpu-1gb"
  region     = var.region
  node_count = 1
}

# Kubernetes cluster
resource "digitalocean_kubernetes_cluster" "vertoie" {
  name    = "vertoie-cluster"
  region  = var.region
  version = "1.28.2-do.0"

  node_pool {
    name       = "worker-pool"
    size       = "s-2vcpu-2gb"
    node_count = 2
  }
}

# Load balancer
resource "digitalocean_loadbalancer" "vertoie" {
  name   = "vertoie-lb"
  region = var.region

  forwarding_rule {
    entry_protocol  = "https"
    entry_port      = 443
    target_protocol = "http"
    target_port     = 80
    certificate_name = digitalocean_certificate.vertoie.name
  }

  healthcheck {
    protocol = "http"
    port     = 80
    path     = "/health"
  }

  droplet_tag = "vertoie-web"
}
```

### Kubernetes Manifests
```yaml
# k8s/core-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vertoie-core
spec:
  replicas: 3
  selector:
    matchLabels:
      app: vertoie-core
  template:
    metadata:
      labels:
        app: vertoie-core
    spec:
      containers:
      - name: core
        image: ghcr.io/vertoie/vertoie/core:latest
        ports:
        - containerPort: 8000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: vertoie-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: vertoie-secrets
              key: redis-url
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: vertoie-secrets
              key: jwt-secret
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: vertoie-core-service
spec:
  selector:
    app: vertoie-core
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: ClusterIP
```

### Monitoring and Logging
```yaml
# k8s/monitoring.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'vertoie-core'
      static_configs:
      - targets: ['vertoie-core-service:80']
      metrics_path: /metrics
      scrape_interval: 5s

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
      volumes:
      - name: config
        configMap:
          name: prometheus-config
```

## Security Configuration

### SSL/TLS Setup
```nginx
# nginx.conf for web frontend
server {
    listen 80;
    server_name vertoie.com www.vertoie.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name vertoie.com www.vertoie.com;

    ssl_certificate /etc/ssl/certs/vertoie.crt;
    ssl_certificate_key /etc/ssl/private/vertoie.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://vertoie-core-service:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws {
        proxy_pass http://vertoie-core-service:80;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

### Environment Secrets Management
```bash
# Create Kubernetes secrets
kubectl create secret generic vertoie-secrets \
  --from-literal=database-url="******************************/vertoie" \
  --from-literal=redis-url="redis://host:6379" \
  --from-literal=jwt-secret="your-jwt-secret" \
  --from-literal=groq-api-key="your-groq-key"
```
