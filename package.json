{"name": "vertoie", "version": "0.1.0", "description": "AI-powered business application platform with cross-platform deployment", "private": true, "type": "module", "packageManager": "pnpm@8.15.0", "workspaces": ["web", "app", "shared"], "scripts": {"dev": "concurrently \"pnpm dev:core\" \"pnpm dev:web\" \"pnpm dev:app\"", "dev:core": "cd core && cargo watch -x run", "dev:web": "cd web && pnpm dev", "dev:app": "cd app && pnpm tauri dev", "build": "pnpm build:core && pnpm build:web && pnpm build:app", "build:core": "cd core && cargo build --release", "build:web": "cd web && pnpm build", "build:app": "cd app && pnpm tauri build", "test": "pnpm test:core && pnpm test:web && pnpm test:app", "test:core": "cd core && cargo test", "test:web": "cd web && pnpm test", "test:app": "cd app && pnpm test", "lint": "pnpm lint:core && pnpm lint:web && pnpm lint:app", "lint:core": "cd core && cargo clippy -- -D warnings", "lint:web": "cd web && pnpm lint", "lint:app": "cd app && pnpm lint", "format": "pnpm format:core && pnpm format:web && pnpm format:app", "format:core": "cd core && cargo fmt", "format:web": "cd web && pnpm format", "format:app": "cd app && pnpm format", "clean": "pnpm clean:core && pnpm clean:web && pnpm clean:app", "clean:core": "cd core && cargo clean", "clean:web": "cd web && rm -rf node_modules dist", "clean:app": "cd app && rm -rf node_modules dist src-tauri/target"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/vertoie/vertoie.git"}, "keywords": ["business-applications", "ai-powered", "cross-platform", "rust", "solidjs", "tauri"], "author": "Vertoie Team", "license": "MIT"}