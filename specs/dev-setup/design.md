# Development Environment Setup - Design

## Architecture

### Project Structure Design
The development environment will be organized around Vertoie's multi-platform architecture:

```
vertoie/
├── core/                    # Rust + Axum backend API
│   ├── src/                # Source code
│   ├── migrations/         # Database migrations
│   ├── Cargo.toml         # Rust dependencies
│   └── .env               # Environment configuration
├── web/                    # SolidJS web platform
│   ├── src/               # Frontend source
│   ├── package.json       # Node dependencies
│   └── vite.config.ts     # Build configuration
├── app/                    # Tauri cross-platform apps
│   ├── src/               # SolidJS frontend
│   ├── src-tauri/         # Tauri backend
│   └── package.json       # Dependencies
├── shared/                 # Shared components/utilities
├── .envrc                 # Direnv configuration (committed)
├── .envrc.local           # Local environment overrides (gitignored)
├── docker-compose.yml     # Local services
└── package.json           # Root workspace scripts
```

### Development Workflow Design
1. **Backend Development**: Rust with hot-reload via cargo-watch
2. **Frontend Development**: SolidJS with Vite dev server and HMR
3. **Desktop Development**: Tauri dev mode with integrated frontend
4. **Database Development**: PostgreSQL with migration-based schema management

## Implementation Plan

### Phase 1: Core Infrastructure Setup
1. **Project Initialization**
   - Create core project structure
   - Initialize Rust workspace with Cargo.toml
   - Set up Node.js workspace with pnpm workspaces
   - Create direnv configuration (.envrc) with local override support

2. **Database Setup**
   - Create PostgreSQL development database
   - Install and configure sqlx-cli for migrations
   - Set up initial database schema
   - Create development data seeding

### Phase 2: Development Tools Configuration
1. **Rust Backend Setup**
   - Configure Cargo.toml with Axum dependencies
   - Set up development dependencies (cargo-watch)
   - Create basic server structure
   - Configure environment variable handling

2. **Frontend Setup**
   - Initialize SolidJS projects for web and app
   - Configure Vite build system
   - Set up TypeScript configuration
   - Configure development servers with proxy

3. **Tauri Integration**
   - Install Tauri CLI
   - Initialize Tauri project structure
   - Configure tauri.conf.json
   - Set up development and build scripts

### Phase 3: Code Quality and Workflow
1. **Linting and Formatting**
   - Configure ESLint for TypeScript/SolidJS
   - Set up Prettier for code formatting
   - Configure Clippy for Rust linting
   - Set up rustfmt configuration

2. **Testing Framework**
   - Set up Vitest for frontend testing
   - Configure Rust testing with cargo test
   - Set up integration test structure
   - Create sample tests

3. **Development Automation**
   - Configure pre-commit hooks
   - Set up development scripts
   - Create hot-reload configurations
   - Set up IDE configurations

### Phase 4: Documentation and Validation
1. **Documentation**
   - Create comprehensive README
   - Document development workflows
   - Create troubleshooting guide
   - Set up API documentation

2. **Validation**
   - Test complete development workflow
   - Validate cross-platform compatibility
   - Performance testing and optimization
   - Create developer onboarding checklist

## Dependencies

### System Requirements
- Rust (stable toolchain)
- Node.js (LTS version)
- PostgreSQL (version 14+)
- Git
- direnv (for environment management)

### Development Tools
- **Rust**: cargo-watch, sqlx-cli
- **Node.js**: pnpm (package manager)
- **Tauri**: @tauri-apps/cli
- **Database**: PostgreSQL client tools

### Key Libraries and Frameworks
- **Backend**: Axum, SQLx, Tokio, Serde
- **Frontend**: SolidJS, Vite, TypeScript
- **Testing**: Vitest (frontend), cargo test (backend)
- **Code Quality**: ESLint, Prettier, Clippy, rustfmt

## Risk Assessment

### High Risk
1. **Database Connection Issues**
   - *Risk*: PostgreSQL connection failures or permission issues
   - *Mitigation*: Provide clear database setup instructions, use Docker fallback

2. **Tauri Platform Dependencies**
   - *Risk*: Platform-specific build dependencies missing
   - *Mitigation*: Document platform requirements, provide installation scripts

### Medium Risk
1. **Package Manager Conflicts**
   - *Risk*: npm/yarn/pnpm conflicts or version mismatches
   - *Mitigation*: Standardize on pnpm, provide clear installation instructions

2. **Environment Configuration**
   - *Risk*: Missing or incorrect environment variables
   - *Mitigation*: Committed .envrc with sensible defaults, .envrc.local for overrides

### Low Risk
1. **IDE Configuration**
   - *Risk*: Inconsistent development experience across editors
   - *Mitigation*: Provide VS Code settings, document alternatives

2. **Hot-reload Performance**
   - *Risk*: Slow development feedback loops
   - *Mitigation*: Optimize build configurations, use efficient watchers

## Success Metrics

### Performance Targets
- Backend compilation: < 30 seconds for incremental builds
- Frontend hot-reload: < 2 seconds for changes
- Database migrations: < 5 seconds for typical changes
- Full project setup: < 30 minutes for new developers

### Quality Targets
- Zero linting errors on fresh setup
- All tests pass in clean environment
- Documentation covers 100% of setup steps
- Cross-platform compatibility verified on macOS, Linux, Windows

### Developer Experience
- Single command to start all development servers
- Clear error messages for common issues
- Automated code formatting and quality checks
- Comprehensive troubleshooting documentation
