# Environment variables
.envrc.local
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/
.pnpm-store/
.pnpm-debug.log*

# Rust
target/
Cargo.lock
**/*.rs.bk
*.pdb

# Build outputs
dist/
build/
out/

# IDE and editor files
.vscode/settings.json
.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Tauri specific
src-tauri/target/
src-tauri/Cargo.lock

# Database
*.db
*.sqlite
*.sqlite3

# Test databases
*_test.db
test_*.db

# SQLx offline data
sqlx-data.json

# Backup files
*.bak
*.backup

# Local development files
.local/
local/

# Docker
.dockerignore

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Keep these files
!.gitkeep
!.envrc
!.envrc.local.example
